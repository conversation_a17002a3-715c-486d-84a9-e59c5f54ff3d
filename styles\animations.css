/* رسوم متحركة وتأثيرات بصرية */

/* تأثيرات الظهور */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -30px, 0);
  }
  70% {
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-10px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(10px);
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes zoomOut {
  from {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(0.3);
  }
  to {
    opacity: 0;
    transform: scale(0.3);
  }
}

@keyframes flipIn {
  from {
    transform: perspective(400px) rotateY(90deg);
    opacity: 0;
  }
  40% {
    transform: perspective(400px) rotateY(-20deg);
  }
  60% {
    transform: perspective(400px) rotateY(10deg);
    opacity: 1;
  }
  80% {
    transform: perspective(400px) rotateY(-5deg);
  }
  to {
    transform: perspective(400px) rotateY(0deg);
    opacity: 1;
  }
}

@keyframes slideInDown {
  from {
    transform: translate3d(0, -100%, 0);
    visibility: visible;
  }
  to {
    transform: translate3d(0, 0, 0);
  }
}

@keyframes slideInUp {
  from {
    transform: translate3d(0, 100%, 0);
    visibility: visible;
  }
  to {
    transform: translate3d(0, 0, 0);
  }
}

@keyframes slideInLeft {
  from {
    transform: translate3d(-100%, 0, 0);
    visibility: visible;
  }
  to {
    transform: translate3d(0, 0, 0);
  }
}

@keyframes slideInRight {
  from {
    transform: translate3d(100%, 0, 0);
    visibility: visible;
  }
  to {
    transform: translate3d(0, 0, 0);
  }
}

/* تأثيرات التحميل */
@keyframes loading {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes loadingDots {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

@keyframes progress {
  from {
    width: 0%;
  }
  to {
    width: 100%;
  }
}

/* تأثيرات النصوص */
@keyframes typewriter {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

@keyframes blinkCursor {
  from, to {
    border-color: transparent;
  }
  50% {
    border-color: var(--primary-color);
  }
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* فئات الرسوم المتحركة */
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

.slide-in {
  animation: slideIn 0.5s ease-out;
}

.bounce {
  animation: bounce 0.6s ease-in-out;
}

.pulse {
  animation: pulse 1s infinite;
}

.shake {
  animation: shake 0.82s cubic-bezier(0.36, 0.07, 0.19, 0.97);
}

.rotate {
  animation: rotate 2s linear infinite;
}

.zoom-in {
  animation: zoomIn 0.5s ease-out;
}

.zoom-out {
  animation: zoomOut 0.5s ease-in;
}

.flip-in {
  animation: flipIn 0.6s ease-in-out;
}

.slide-in-down {
  animation: slideInDown 0.5s ease-out;
}

.slide-in-up {
  animation: slideInUp 0.5s ease-out;
}

.slide-in-left {
  animation: slideInLeft 0.5s ease-out;
}

.slide-in-right {
  animation: slideInRight 0.5s ease-out;
}

/* مؤشرات التحميل */
.loading-spinner {
  border: 4px solid #f3f3f3;
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: loading 1s linear infinite;
  margin: 20px auto;
}

.loading-dots {
  display: inline-block;
}

.loading-dots::after {
  content: '...';
  animation: loadingDots 1.4s infinite;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.progress-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: var(--primary-color);
  animation: progress 2s ease-in-out;
}

/* تأثيرات التفاعل */
.hover-lift {
  transition: transform 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
}

.hover-glow {
  transition: box-shadow 0.3s ease;
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(52, 152, 219, 0.5);
}

.hover-scale {
  transition: transform 0.3s ease;
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-rotate {
  transition: transform 0.3s ease;
}

.hover-rotate:hover {
  transform: rotate(5deg);
}

/* تأثيرات الخلفية */
.animated-background {
  background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
}

.floating-shapes {
  position: relative;
  overflow: hidden;
}

.floating-shapes::before,
.floating-shapes::after {
  content: '';
  position: absolute;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
}

.floating-shapes::before {
  width: 80px;
  height: 80px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.floating-shapes::after {
  width: 120px;
  height: 120px;
  top: 70%;
  right: 10%;
  animation-delay: 3s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* تأثيرات النصوص المتحركة */
.typewriter {
  overflow: hidden;
  border-left: 2px solid var(--primary-color);
  white-space: nowrap;
  animation: typewriter 3s steps(40, end), blinkCursor 0.75s step-end infinite;
}

.text-glow {
  text-shadow: 0 0 10px var(--primary-color);
  animation: pulse 2s ease-in-out infinite alternate;
}

.rainbow-text {
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3);
  background-size: 400% 400%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradientShift 3s ease infinite;
}

/* تأثيرات الأزرار */
.btn-animated {
  position: relative;
  overflow: hidden;
}

.btn-animated::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.btn-animated:hover::before {
  left: 100%;
}

.btn-ripple {
  position: relative;
  overflow: hidden;
}

.btn-ripple::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.btn-ripple:active::after {
  width: 300px;
  height: 300px;
}

/* تأثيرات الكروت */
.card-flip {
  perspective: 1000px;
}

.card-flip-inner {
  position: relative;
  width: 100%;
  height: 100%;
  text-align: center;
  transition: transform 0.6s;
  transform-style: preserve-3d;
}

.card-flip:hover .card-flip-inner {
  transform: rotateY(180deg);
}

.card-flip-front,
.card-flip-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
}

.card-flip-back {
  transform: rotateY(180deg);
}

/* تأثيرات التركيز */
.focus-ring {
  transition: box-shadow 0.15s ease-in-out;
}

.focus-ring:focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

/* تأثيرات الجداول */
.table-animated tbody tr {
  transition: all 0.3s ease;
}

.table-animated tbody tr:hover {
  background-color: var(--primary-color);
  color: white;
  transform: scale(1.02);
}

/* تأثيرات القوائم */
.list-animated li {
  opacity: 0;
  transform: translateX(-20px);
  animation: slideInLeft 0.5s ease forwards;
}

.list-animated li:nth-child(1) { animation-delay: 0.1s; }
.list-animated li:nth-child(2) { animation-delay: 0.2s; }
.list-animated li:nth-child(3) { animation-delay: 0.3s; }
.list-animated li:nth-child(4) { animation-delay: 0.4s; }
.list-animated li:nth-child(5) { animation-delay: 0.5s; }

/* تأثيرات النوافذ المنبثقة */
.modal-animated {
  opacity: 0;
  transform: scale(0.7);
  transition: all 0.3s ease;
}

.modal-animated.show {
  opacity: 1;
  transform: scale(1);
}

/* تأثيرات خاصة للأرشيف */
.archive-transition {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.record-slide-in {
  animation: slideInRight 0.6s ease-out;
}

.record-slide-out {
  animation: slideInLeft 0.6s ease-out reverse;
}

/* تأثيرات الاستجابة */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* تأثيرات الطباعة */
@media print {
  * {
    animation: none !important;
    transition: none !important;
  }
}
