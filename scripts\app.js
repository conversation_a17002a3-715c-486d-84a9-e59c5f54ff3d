// فحص دعم المتصفح والتوافق
(function() {
  'use strict';

  // فحص دعم localStorage
  function checkLocalStorageSupport() {
    try {
      var test = 'test';
      localStorage.setItem(test, test);
      localStorage.removeItem(test);
      return true;
    } catch(e) {
      return false;
    }
  }

  // فحص دعم JSON
  function checkJSONSupport() {
    return typeof JSON !== 'undefined' && JSON.parse && JSON.stringify;
  }

  // إضافة polyfills للمتصفحات القديمة

  // Array.forEach polyfill
  if (!Array.prototype.forEach) {
    Array.prototype.forEach = function(callback, thisArg) {
      if (this == null) throw new TypeError('Array.prototype.forEach called on null or undefined');
      var T, k;
      var O = Object(this);
      var len = parseInt(O.length) || 0;
      if (typeof callback !== "function") throw new TypeError(callback + ' is not a function');
      if (arguments.length > 1) T = thisArg;
      k = 0;
      while (k < len) {
        var kValue;
        if (k in O) {
          kValue = O[k];
          callback.call(T, kValue, k, O);
        }
        k++;
      }
    };
  }

  // Array.filter polyfill
  if (!Array.prototype.filter) {
    Array.prototype.filter = function(fun) {
      if (this === void 0 || this === null) throw new TypeError();
      var t = Object(this);
      var len = parseInt(t.length) || 0;
      if (typeof fun !== 'function') throw new TypeError();
      var res = [];
      var thisArg = arguments.length >= 2 ? arguments[1] : void 0;
      for (var i = 0; i < len; i++) {
        if (i in t) {
          var val = t[i];
          if (fun.call(thisArg, val, i, t)) res.push(val);
        }
      }
      return res;
    };
  }

  // Array.map polyfill
  if (!Array.prototype.map) {
    Array.prototype.map = function(callback, thisArg) {
      if (this == null) throw new TypeError('Array.prototype.map called on null or undefined');
      var T, A, k;
      var O = Object(this);
      var len = parseInt(O.length) || 0;
      if (typeof callback !== 'function') throw new TypeError(callback + ' is not a function');
      if (arguments.length > 1) T = thisArg;
      A = new Array(len);
      k = 0;
      while (k < len) {
        var kValue, mappedValue;
        if (k in O) {
          kValue = O[k];
          mappedValue = callback.call(T, kValue, k, O);
          A[k] = mappedValue;
        }
        k++;
      }
      return A;
    };
  }

  // Array.some polyfill
  if (!Array.prototype.some) {
    Array.prototype.some = function(fun) {
      if (this == null) throw new TypeError('Array.prototype.some called on null or undefined');
      var t = Object(this);
      var len = parseInt(t.length) || 0;
      if (typeof fun !== 'function') throw new TypeError();
      var thisArg = arguments.length >= 2 ? arguments[1] : void 0;
      for (var i = 0; i < len; i++) {
        if (i in t && fun.call(thisArg, t[i], i, t)) return true;
      }
      return false;
    };
  }

  // Array.every polyfill
  if (!Array.prototype.every) {
    Array.prototype.every = function(fun) {
      if (this == null) throw new TypeError('Array.prototype.every called on null or undefined');
      var t = Object(this);
      var len = parseInt(t.length) || 0;
      if (typeof fun !== 'function') throw new TypeError();
      var thisArg = arguments.length >= 2 ? arguments[1] : void 0;
      for (var i = 0; i < len; i++) {
        if (i in t && !fun.call(thisArg, t[i], i, t)) return false;
      }
      return true;
    };
  }

  // Object.keys polyfill
  if (!Object.keys) {
    Object.keys = function(obj) {
      var keys = [];
      for (var key in obj) {
        if (obj.hasOwnProperty(key)) {
          keys.push(key);
        }
      }
      return keys;
    };
  }

  // Date.now polyfill
  if (!Date.now) {
    Date.now = function() {
      return new Date().getTime();
    };
  }

  // تحذير للمتصفحات غير المدعومة
  if (!checkLocalStorageSupport() || !checkJSONSupport()) {
    setTimeout(function() {
      alert('تحذير: متصفحك قديم وقد لا يدعم جميع ميزات البرنامج. يُنصح بتحديث المتصفح للحصول على أفضل تجربة.');
    }, 1000);
  }
})();

// متغيرات التطبيق الرئيسية
// المطور: المحاسب المبرمج علي عاجل خشان المحنة
// برنامج الأرشفة الإلكترونية - الإصدار 2.0

var archiveData = [];
var currentTheme = 'default';
var currentRecordIndex = 0;
var filteredData = [];

// مسار التخزين في القرص D
var STORAGE_PATH = 'D:/ArchiveData/';

// تحميل البيانات بطريقة آمنة
function initializeApp() {
  try {
    // تحميل البيانات من قاعدة البيانات
    if (typeof db !== 'undefined' && db.getAllRecords) {
      archiveData = db.getAllRecords();
    } else {
      // fallback للتحميل من localStorage
      var savedData = localStorage.getItem('archiveData');
      if (savedData) {
        archiveData = JSON.parse(savedData);
      }
    }

    var savedTheme = localStorage.getItem('currentTheme');
    if (savedTheme) {
      currentTheme = savedTheme;
    }

    console.log('تم تحميل البيانات بنجاح:', archiveData.length, 'سجل');

    // إنشاء مجلد التخزين في القرص D
    createStorageFolder();

  } catch(e) {
    console.warn('تعذر تحميل البيانات المحفوظة:', e);
    archiveData = [];
  }
}

// تشغيل التطبيق
initializeApp();

// ===== الدوال الأساسية للتطبيق =====

// إظهار صفحة معينة
function showPage(pageId) {
  // إخفاء جميع الصفحات
  var pages = document.querySelectorAll('.page');
  for (var i = 0; i < pages.length; i++) {
    pages[i].style.display = 'none';
  }

  // إزالة الفئة النشطة من جميع أزرار التنقل
  var navBtns = document.querySelectorAll('.nav-btn');
  for (var i = 0; i < navBtns.length; i++) {
    navBtns[i].classList.remove('active');
  }

  // إظهار الصفحة المطلوبة
  var targetPage = document.getElementById(pageId);
  if (targetPage) {
    targetPage.style.display = 'block';

    // تفعيل زر التنقل المناسب
    var activeBtn = document.querySelector('[onclick="showPage(\'' + pageId + '\')"]');
    if (activeBtn) {
      activeBtn.classList.add('active');
    }

    // تحديث محتوى الصفحة حسب نوعها
    updatePageContent(pageId);
  }
}

// تحديث محتوى الصفحة
function updatePageContent(pageId) {
  switch(pageId) {
    case 'welcomePage':
      updateStatistics();
      break;
    case 'archivePage':
      displayArchiveRecords();
      break;
    case 'reportPage':
      generateReport();
      break;
    case 'settingsPage':
      updateSettingsInfo();
      break;
  }
}

// إضافة سجل جديد
function addRecord() {
  var formData = {
    bookNumber: document.getElementById('bookNumber').value.trim(),
    day: parseInt(document.getElementById('day').value),
    month: parseInt(document.getElementById('month').value),
    year: parseInt(document.getElementById('year').value),
    subject: document.getElementById('subject').value.trim(),
    content: document.getElementById('content').value.trim(),
    department: document.getElementById('department').value.trim(),
    notes: document.getElementById('notes').value.trim()
  };

  // التحقق من صحة البيانات
  var validation = validateRecordData(formData);
  if (!validation.isValid) {
    showNotification(validation.message, 'error');
    return;
  }

  // التحقق من عدم تكرار رقم الكتاب
  if (archiveData.some(function(record) { return record.bookNumber === formData.bookNumber; })) {
    showNotification('رقم الكتاب موجود مسبقاً', 'error');
    return;
  }

  var newRecord = {
    id: generateUniqueId(),
    bookNumber: formData.bookNumber,
    day: formData.day,
    month: formData.month,
    year: formData.year,
    date: formData.day + '/' + formData.month + '/' + formData.year,
    subject: formData.subject,
    content: formData.content,
    department: formData.department,
    notes: formData.notes,
    createdAt: new Date().toISOString(),
    modifiedAt: new Date().toISOString()
  };

  // إضافة السجل
  archiveData.push(newRecord);

  // حفظ البيانات
  saveData();

  // مسح النموذج
  document.getElementById('dataForm').reset();

  showNotification('تم حفظ السجل بنجاح', 'success');

  // تحديث الإحصائيات
  updateStatistics();
}

// التحقق من صحة البيانات
function validateRecordData(data) {
  var errors = [];

  if (!data.bookNumber) errors.push('رقم الكتاب مطلوب');
  if (!data.day || !data.month || !data.year) errors.push('التاريخ مطلوب');
  if (!data.subject) errors.push('الموضوع مطلوب');
  if (!data.content) errors.push('المحتوى مطلوب');
  if (!data.department) errors.push('الدائرة مطلوبة');

  // التحقق من صحة التاريخ
  if (data.day && data.month && data.year) {
    if (data.day < 1 || data.day > 31) errors.push('اليوم غير صحيح');
    if (data.month < 1 || data.month > 12) errors.push('الشهر غير صحيح');
    if (data.year < 1900 || data.year > 2100) errors.push('السنة غير صحيحة');
  }

  return {
    isValid: errors.length === 0,
    message: errors.join('، ')
  };
}

// توليد معرف فريد
function generateUniqueId() {
  return Date.now() + Math.random().toString(36).substr(2, 9);
}

// حفظ البيانات
function saveData() {
  try {
    // حفظ في localStorage
    localStorage.setItem('archiveData', JSON.stringify(archiveData));

    // حفظ في قاعدة البيانات إذا كانت متاحة
    if (typeof db !== 'undefined' && db.saveData) {
      db.saveData(archiveData);
    }

    return true;
  } catch(e) {
    console.error('خطأ في حفظ البيانات:', e);
    return false;
  }
}

// تحديث الإحصائيات
function updateStatistics() {
  try {
    var totalRecords = archiveData.length;
    var today = new Date();
    var todayRecords = archiveData.filter(function(record) {
      var recordDate = new Date(record.year, record.month - 1, record.day);
      return recordDate.toDateString() === today.toDateString();
    }).length;

    var departments = [];
    for (var i = 0; i < archiveData.length; i++) {
      if (archiveData[i].department && departments.indexOf(archiveData[i].department) === -1) {
        departments.push(archiveData[i].department);
      }
    }

    var totalElement = document.getElementById('totalRecordsCount');
    var todayElement = document.getElementById('todayRecordsCount');
    var deptElement = document.getElementById('departmentsCount');

    if (totalElement) totalElement.textContent = totalRecords;
    if (todayElement) todayElement.textContent = todayRecords;
    if (deptElement) deptElement.textContent = departments.length;
  } catch(e) {
    console.error('خطأ في تحديث الإحصائيات:', e);
  }
}

// عرض سجلات الأرشيف
function displayArchiveRecords(data) {
  var recordsToShow = data || archiveData;
  filteredData = recordsToShow;

  var recordDisplay = document.getElementById('recordDisplay');
  var recordCounter = document.getElementById('recordCounter');

  if (!recordDisplay) return;

  if (recordsToShow.length === 0) {
    recordDisplay.innerHTML = '<p class="no-records">لا توجد سجلات للعرض</p>';
    if (recordCounter) recordCounter.textContent = '0 من 0';
    updateNavigationButtons();
    return;
  }

  // التأكد من أن المؤشر في النطاق الصحيح
  if (currentRecordIndex >= recordsToShow.length) {
    currentRecordIndex = recordsToShow.length - 1;
  }
  if (currentRecordIndex < 0) {
    currentRecordIndex = 0;
  }

  var record = recordsToShow[currentRecordIndex];

  recordDisplay.innerHTML =
    '<div class="record-card">' +
      '<div class="record-header">' +
        '<h3>رقم الكتاب: ' + record.bookNumber + '</h3>' +
        '<span class="record-date">' + record.day + '/' + record.month + '/' + record.year + '</span>' +
      '</div>' +
      '<div class="record-body">' +
        '<div class="record-field">' +
          '<label>الموضوع:</label>' +
          '<p>' + record.subject + '</p>' +
        '</div>' +
        '<div class="record-field">' +
          '<label>المحتوى:</label>' +
          '<p>' + record.content + '</p>' +
        '</div>' +
        '<div class="record-field">' +
          '<label>الدائرة:</label>' +
          '<p>' + (record.department || 'غير محدد') + '</p>' +
        '</div>' +
        (record.notes ?
        '<div class="record-field">' +
          '<label>الهامش:</label>' +
          '<p>' + record.notes + '</p>' +
        '</div>' : '') +
      '</div>' +
      '<div class="record-actions">' +
        '<button onclick="editRecord(' + currentRecordIndex + ')" class="btn btn-warning">تعديل</button>' +
        '<button onclick="deleteRecord(' + currentRecordIndex + ')" class="btn btn-danger">حذف</button>' +
      '</div>' +
    '</div>';

  if (recordCounter) {
    recordCounter.textContent = (currentRecordIndex + 1) + ' من ' + recordsToShow.length;
  }
  updateNavigationButtons();
}

// تحديث أزرار التنقل
function updateNavigationButtons() {
  var firstBtn = document.getElementById('firstBtn');
  var prevBtn = document.getElementById('prevBtn');
  var nextBtn = document.getElementById('nextBtn');
  var lastBtn = document.getElementById('lastBtn');

  var hasRecords = filteredData.length > 0;
  var isFirst = currentRecordIndex === 0;
  var isLast = currentRecordIndex === filteredData.length - 1;

  if (firstBtn) firstBtn.disabled = !hasRecords || isFirst;
  if (prevBtn) prevBtn.disabled = !hasRecords || isFirst;
  if (nextBtn) nextBtn.disabled = !hasRecords || isLast;
  if (lastBtn) lastBtn.disabled = !hasRecords || isLast;
}

// التنقل في السجلات
function firstRecord() {
  currentRecordIndex = 0;
  displayArchiveRecords(filteredData);
}

function prevRecord() {
  if (currentRecordIndex > 0) {
    currentRecordIndex--;
    displayArchiveRecords(filteredData);
  }
}

function nextRecord() {
  if (currentRecordIndex < filteredData.length - 1) {
    currentRecordIndex++;
    displayArchiveRecords(filteredData);
  }
}

function lastRecord() {
  currentRecordIndex = filteredData.length - 1;
  displayArchiveRecords(filteredData);
}

// البحث السريع
function quickSearch() {
  var searchInput = document.getElementById('quickSearchInput');
  if (!searchInput) return;

  var searchTerm = searchInput.value.trim().toLowerCase();
  if (!searchTerm) {
    displayArchiveRecords();
    return;
  }

  var results = archiveData.filter(function(record) {
    return record.bookNumber.toLowerCase().includes(searchTerm) ||
           record.subject.toLowerCase().includes(searchTerm) ||
           record.content.toLowerCase().includes(searchTerm) ||
           (record.department && record.department.toLowerCase().includes(searchTerm)) ||
           (record.notes && record.notes.toLowerCase().includes(searchTerm));
  });

  currentRecordIndex = 0;
  displayArchiveRecords(results);
  showPage('archivePage');

  if (results.length === 0) {
    showNotification('لم يتم العثور على نتائج', 'info');
  } else {
    showNotification('تم العثور على ' + results.length + ' نتيجة', 'success');
  }
}

// تطبيق التصفية المتقدمة
function applyFilter() {
  var filters = {
    bookNumber: document.getElementById('bookNumberFilter').value.trim(),
    subject: document.getElementById('subjectFilter').value.trim(),
    employee: document.getElementById('employeeFilter').value.trim(),
    department: document.getElementById('departmentFilter').value.trim(),
    notes: document.getElementById('notesFilter').value
  };

  var dateFilters = {
    fromDay: parseInt(document.getElementById('fromDay').value) || null,
    fromMonth: parseInt(document.getElementById('fromMonth').value) || null,
    fromYear: parseInt(document.getElementById('fromYear').value) || null,
    toDay: parseInt(document.getElementById('toDay').value) || null,
    toMonth: parseInt(document.getElementById('toMonth').value) || null,
    toYear: parseInt(document.getElementById('toYear').value) || null
  };

  var results = archiveData.filter(function(record) {
    // تصفية النص
    if (filters.bookNumber && !record.bookNumber.toLowerCase().includes(filters.bookNumber.toLowerCase())) {
      return false;
    }
    if (filters.subject && !record.subject.toLowerCase().includes(filters.subject.toLowerCase())) {
      return false;
    }
    if (filters.employee && !record.content.toLowerCase().includes(filters.employee.toLowerCase())) {
      return false;
    }
    if (filters.department && !record.department.toLowerCase().includes(filters.department.toLowerCase())) {
      return false;
    }

    // تصفية الهوامش
    if (filters.notes) {
      if (filters.notes === 'has-notes' && (!record.notes || !record.notes.trim())) {
        return false;
      }
      if (filters.notes === 'no-notes' && record.notes && record.notes.trim()) {
        return false;
      }
    }

    // تصفية التاريخ
    if (dateFilters.fromDay && dateFilters.fromMonth && dateFilters.fromYear) {
      var fromDate = new Date(dateFilters.fromYear, dateFilters.fromMonth - 1, dateFilters.fromDay);
      var recordDate = new Date(record.year, record.month - 1, record.day);
      if (recordDate < fromDate) return false;
    }

    if (dateFilters.toDay && dateFilters.toMonth && dateFilters.toYear) {
      var toDate = new Date(dateFilters.toYear, dateFilters.toMonth - 1, dateFilters.toDay);
      var recordDate = new Date(record.year, record.month - 1, record.day);
      if (recordDate > toDate) return false;
    }

    return true;
  });

  currentRecordIndex = 0;
  displayArchiveRecords(results);
  showPage('archivePage');

  showNotification('تم العثور على ' + results.length + ' نتيجة', 'success');
}

// إلغاء التصفية
function clearFilter() {
  // مسح حقول التصفية
  var filterInputs = document.querySelectorAll('#filterForm input, #filterForm select');
  for (var i = 0; i < filterInputs.length; i++) {
    filterInputs[i].value = '';
  }

  // عرض جميع السجلات
  currentRecordIndex = 0;
  displayArchiveRecords();

  showNotification('تم إلغاء التصفية', 'info');
}

// إنشاء التقرير
function generateReport() {
  var reportTableBody = document.getElementById('reportTableBody');
  if (!reportTableBody) return;

  if (archiveData.length === 0) {
    reportTableBody.innerHTML = '<tr><td colspan="7">لا توجد بيانات للعرض</td></tr>';
    return;
  }

  var html = '';
  for (var i = 0; i < archiveData.length; i++) {
    var record = archiveData[i];
    html += '<tr>' +
      '<td>' + (i + 1) + '</td>' +
      '<td>' + record.bookNumber + '</td>' +
      '<td>' + record.day + '/' + record.month + '/' + record.year + '</td>' +
      '<td>' + record.subject + '</td>' +
      '<td>' + record.content + '</td>' +
      '<td>' + (record.department || 'غير محدد') + '</td>' +
      '<td>' + (record.notes || '-') + '</td>' +
    '</tr>';
  }

  reportTableBody.innerHTML = html;
}

// تصدير البيانات
function exportData() {
  try {
    var exportData = {
      exportDate: new Date().toISOString(),
      developer: 'المحاسب المبرمج علي عاجل خشان المحنة',
      version: '2.0.0',
      totalRecords: archiveData.length,
      data: archiveData
    };

    var fileName = 'archive_backup_' + new Date().toISOString().split('T')[0] + '.json';

    var blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    var url = URL.createObjectURL(blob);
    var a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    showNotification('تم تصدير البيانات بنجاح', 'success');
  } catch(e) {
    console.error('خطأ في التصدير:', e);
    showNotification('حدث خطأ أثناء التصدير: ' + e.message, 'error');
  }
}

// إنشاء مجلد التخزين في القرص D
function createStorageFolder() {
  // هذه الدالة تعمل فقط في بيئة Electron أو Node.js
  if (typeof require !== 'undefined') {
    try {
      var fs = require('fs');
      var path = require('path');

      if (!fs.existsSync(STORAGE_PATH)) {
        fs.mkdirSync(STORAGE_PATH, { recursive: true });
        console.log('تم إنشاء مجلد التخزين:', STORAGE_PATH);
      }
    } catch(e) {
      console.log('لا يمكن إنشاء مجلد التخزين في المتصفح');
    }
  }
}

// تصدير البيانات إلى القرص D
function exportToD() {
  // هذه الدالة تعمل فقط في بيئة Electron أو Node.js
  if (typeof require !== 'undefined') {
    try {
      var fs = require('fs');
      var path = require('path');

      var exportData = {
        exportDate: new Date().toISOString(),
        developer: 'المحاسب المبرمج علي عاجل خشان المحنة',
        version: '2.0.0',
        totalRecords: archiveData.length,
        data: archiveData
      };

      var fileName = 'archive_data.json';
      var filePath = path.join(STORAGE_PATH, fileName);

      fs.writeFileSync(filePath, JSON.stringify(exportData, null, 2), 'utf8');
      console.log('تم حفظ البيانات في:', filePath);
    } catch(e) {
      console.log('لا يمكن الحفظ في القرص D من المتصفح');
    }
  }
}

// استيراد البيانات
function importData(event) {
  var file = event.target.files[0];
  if (!file) return;

  var reader = new FileReader();
  reader.onload = function(e) {
    try {
      var importedData = JSON.parse(e.target.result);

      if (importedData.data && Array.isArray(importedData.data)) {
        if (confirm('سيتم استبدال البيانات الحالية. هل تريد المتابعة؟')) {
          archiveData = importedData.data;
          saveData();
          updateStatistics();
          showNotification('تم استيراد البيانات بنجاح', 'success');

          // إعادة تحميل الصفحة لتحديث العرض
          setTimeout(function() {
            location.reload();
          }, 1000);
        }
      } else {
        showNotification('ملف البيانات غير صحيح', 'error');
      }
    } catch(e) {
      showNotification('خطأ في قراءة الملف: ' + e.message, 'error');
    }
  };

  reader.readAsText(file);
}

// تطبيق الثيم
function applyTheme(themeName) {
  document.body.className = document.body.className.replace(/theme-\w+/g, '');
  document.body.classList.add('theme-' + themeName);
  currentTheme = themeName;
  localStorage.setItem('currentTheme', themeName);

  // تحديث أزرار الثيم
  var themeButtons = document.querySelectorAll('.theme-btn');
  for (var i = 0; i < themeButtons.length; i++) {
    themeButtons[i].classList.remove('active');
  }

  var activeThemeBtn = document.querySelector('[onclick="applyTheme(\'' + themeName + '\')"]');
  if (activeThemeBtn) {
    activeThemeBtn.classList.add('active');
  }

  showNotification('تم تطبيق الثيم: ' + getThemeName(themeName), 'success');
}

// الحصول على اسم الثيم بالعربية
function getThemeName(themeName) {
  var themeNames = {
    'default': 'افتراضي',
    'ocean': 'محيطي',
    'sunset': 'غروب',
    'forest': 'غابة'
  };
  return themeNames[themeName] || themeName;
}

// تحديث معلومات الإعدادات
function updateSettingsInfo() {
  var dataSize = JSON.stringify(archiveData).length;
  var dbSizeElement = document.getElementById('dbSize');
  var lastUpdateElement = document.getElementById('lastUpdate');

  if (dbSizeElement) {
    dbSizeElement.textContent = (dataSize / 1024).toFixed(2) + ' KB';
  }
  if (lastUpdateElement) {
    lastUpdateElement.textContent = new Date().toLocaleDateString('ar-SA');
  }
}

// إظهار الإحصائيات التفصيلية
function showStatistics() {
  var stats = calculateDetailedStatistics();

  var message = 'إحصائيات مفصلة:\n\n' +
    'إجمالي السجلات: ' + stats.total + '\n' +
    'سجلات هذا الشهر: ' + stats.thisMonth + '\n' +
    'سجلات هذا العام: ' + stats.thisYear + '\n' +
    'عدد الدوائر: ' + stats.departments + '\n' +
    'السجلات مع هوامش: ' + stats.withNotes + '\n' +
    'متوسط السجلات شهرياً: ' + stats.avgPerMonth;

  alert(message);
}

// حساب الإحصائيات التفصيلية
function calculateDetailedStatistics() {
  var now = new Date();
  var thisMonth = archiveData.filter(function(record) {
    return record.year === now.getFullYear() && record.month === (now.getMonth() + 1);
  }).length;

  var thisYear = archiveData.filter(function(record) {
    return record.year === now.getFullYear();
  }).length;

  var departments = [];
  var withNotes = 0;

  for (var i = 0; i < archiveData.length; i++) {
    var record = archiveData[i];
    if (record.department && departments.indexOf(record.department) === -1) {
      departments.push(record.department);
    }
    if (record.notes && record.notes.trim()) {
      withNotes++;
    }
  }

  var avgPerMonth = archiveData.length > 0 ? (archiveData.length / 12).toFixed(1) : 0;

  return {
    total: archiveData.length,
    thisMonth: thisMonth,
    thisYear: thisYear,
    departments: departments.length,
    withNotes: withNotes,
    avgPerMonth: avgPerMonth
  };
}

// إظهار الإشعارات
function showNotification(message, type, duration) {
  type = type || 'info';
  duration = duration || 3000;

  var notification = document.createElement('div');
  notification.className = 'notification notification-' + type;
  notification.innerHTML =
    '<div class="notification-content">' +
      '<span class="notification-icon">' + getNotificationIcon(type) + '</span>' +
      '<span class="notification-message">' + message + '</span>' +
      '<button class="notification-close" onclick="closeNotification(this)">&times;</button>' +
    '</div>';

  document.body.appendChild(notification);

  setTimeout(function() {
    notification.classList.add('show');
  }, 100);

  setTimeout(function() {
    if (notification.parentNode) {
      notification.classList.remove('show');
      setTimeout(function() {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 300);
    }
  }, duration);
}

// الحصول على أيقونة الإشعار
function getNotificationIcon(type) {
  var icons = {
    'success': '✅',
    'error': '❌',
    'warning': '⚠️',
    'info': 'ℹ️'
  };
  return icons[type] || icons.info;
}

// إغلاق الإشعار
function closeNotification(button) {
  var notification = button.closest('.notification');
  if (notification && notification.parentNode) {
    notification.classList.remove('show');
    setTimeout(function() {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 300);
  }
}

// تعديل سجل
function editRecord(index) {
  var record = filteredData[index];
  if (!record) return;

  // ملء النموذج بالبيانات الحالية
  document.getElementById('bookNumber').value = record.bookNumber;
  document.getElementById('day').value = record.day;
  document.getElementById('month').value = record.month;
  document.getElementById('year').value = record.year;
  document.getElementById('subject').value = record.subject;
  document.getElementById('content').value = record.content;
  document.getElementById('department').value = record.department || '';
  document.getElementById('notes').value = record.notes || '';

  // حفظ معرف السجل للتحديث
  window.editingRecordId = record.id;

  // الانتقال لصفحة الإدخال
  showPage('dataEntryPage');

  showNotification('تم تحميل بيانات السجل للتعديل', 'info');
}

// حذف سجل
function deleteRecord(index) {
  var record = filteredData[index];
  if (!record) return;

  if (confirm('هل أنت متأكد من حذف هذا السجل؟\n\nرقم الكتاب: ' + record.bookNumber + '\nالموضوع: ' + record.subject)) {
    // العثور على السجل في البيانات الأصلية وحذفه
    for (var i = 0; i < archiveData.length; i++) {
      if (archiveData[i].id === record.id) {
        archiveData.splice(i, 1);
        break;
      }
    }

    // حفظ البيانات
    saveData();

    // تحديث العرض
    if (currentRecordIndex >= filteredData.length - 1) {
      currentRecordIndex = Math.max(0, filteredData.length - 2);
    }

    displayArchiveRecords();
    updateStatistics();

    showNotification('تم حذف السجل بنجاح', 'success');
  }
}

// تحميل الثيم والبيانات عند بدء التطبيق
document.addEventListener('DOMContentLoaded', function() {
  // تطبيق الثيم المحفوظ
  var savedTheme = localStorage.getItem('currentTheme') || 'default';
  applyTheme(savedTheme);

  // إظهار الصفحة الرئيسية
  showPage('welcomePage');

  // تحديث الإحصائيات
  updateStatistics();

  // عرض رسالة ترحيب
  setTimeout(function() {
    showNotification('مرحباً بك في برنامج الأرشفة الإلكترونية\nالمطور: المحاسب المبرمج علي عاجل خشان المحنة', 'info', 5000);
  }, 1000);

  // حفظ البيانات كل 5 دقائق
  setInterval(function() {
    if (archiveData.length > 0) {
      saveData();
    }
  }, 300000);
});

// تطبيق الثيم المحفوظ عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', async function() {
  // تحميل البيانات من نظام الملفات إذا كان متاحاً
  if (window.electronAPI) {
    try {
      const data = await window.electronAPI.loadData();
      if (Array.isArray(data)) {
        archiveData = data;
        console.log('تم تحميل البيانات من نظام الملفات');
      }
    } catch (error) {
      console.error('خطأ في تحميل البيانات من نظام الملفات:', error);
      // استخدام البيانات المخزنة في localStorage كبديل
      const stored = localStorage.getItem('archiveData');
      if (stored) {
        try {
          archiveData = JSON.parse(stored);
        } catch (e) {
          console.error('خطأ في تحليل البيانات المخزنة:', e);
        }
      }
    }
  }
  
  // تطبيق الثيم وتحميل الصفحة الرئيسية
  applyTheme(currentTheme);
  loadWelcomePage();
  
  // تحديث الإحصائيات
  updateStatistics();
});

// وظائف التنقل
function showPage(pageId) {
  // إخفاء جميع الصفحات
  const pages = document.querySelectorAll('.page');
  pages.forEach(page => {
    page.style.display = 'none';
    page.classList.remove('fade-in');
  });

  // إظهار الصفحة المحددة مع تأثير الظهور
  const targetPage = document.getElementById(pageId);
  if (targetPage) {
    targetPage.style.display = 'block';
    setTimeout(() => {
      targetPage.classList.add('fade-in');
    }, 50);
  }

  // تحديث البيانات حسب الصفحة
  if (pageId === 'reportPage') {
    displayAllRecords();
  } else if (pageId === 'archivePage') {
    displayArchiveRecords();
  }
}

function loadWelcomePage() {
  showPage('welcomePage');
}

// وظائف إدخال البيانات المحسنة
function addRecord() {
  const formData = {
    bookNumber: document.getElementById('bookNumber').value.trim(),
    day: document.getElementById('day').value,
    month: document.getElementById('month').value,
    year: document.getElementById('year').value,
    subject: document.getElementById('subject').value.trim(),
    content: document.getElementById('content').value.trim(),
    department: document.getElementById('department').value.trim(),
    notes: document.getElementById('notes').value.trim()
  };

  // التحقق الشامل من صحة البيانات
  const validation = validateRecordData(formData);
  if (!validation.isValid) {
    showNotification(validation.message, 'error');
    highlightInvalidFields(validation.invalidFields);
    return;
  }

  // التحقق من عدم تكرار رقم الكتاب
  if (archiveData.some(record => record.bookNumber === formData.bookNumber)) {
    showNotification('رقم الكتاب موجود مسبقاً', 'error');
    highlightInvalidFields(['bookNumber']);
    return;
  }

  // إنشاء سجل جديد مع معلومات إضافية
  const record = {
    id: generateUniqueId(),
    bookNumber: formData.bookNumber,
    date: `${formData.day}/${formData.month}/${formData.year}`,
    day: parseInt(formData.day),
    month: parseInt(formData.month),
    year: parseInt(formData.year),
    subject: formData.subject,
    content: formData.content,
    department: formData.department,
    notes: formData.notes,
    createdAt: new Date().toISOString(),
    modifiedAt: new Date().toISOString(),
    version: 1
  };

  // إضافة السجل إلى قاعدة البيانات
  archiveData.push(record);
  saveData();

  // إعادة تعيين النموذج ومسح التمييز
  document.getElementById('dataForm').reset();
  clearFieldHighlights();

  showNotification('تم حفظ السجل بنجاح', 'success');
  playSound('success');

  // تحديث الإحصائيات
  updateStatistics();

  // إضافة إلى سجل العمليات
  logActivity('add', record.id, `تم إضافة سجل جديد: ${record.bookNumber}`);
}

// التحقق الشامل من صحة البيانات
function validateRecordData(data) {
  const errors = [];
  const invalidFields = [];

  // التحقق من الحقول المطلوبة
  if (!data.bookNumber) {
    errors.push('رقم الكتاب مطلوب');
    invalidFields.push('bookNumber');
  }

  if (!data.day || !data.month || !data.year) {
    errors.push('التاريخ مطلوب');
    invalidFields.push('day', 'month', 'year');
  }

  if (!data.subject) {
    errors.push('الموضوع مطلوب');
    invalidFields.push('subject');
  }

  if (!data.content) {
    errors.push('المحتوى مطلوب');
    invalidFields.push('content');
  }

  if (!data.department) {
    errors.push('الدائرة مطلوبة');
    invalidFields.push('department');
  }

  // التحقق من صحة التاريخ
  if (data.day && data.month && data.year) {
    const day = parseInt(data.day);
    const month = parseInt(data.month);
    const year = parseInt(data.year);

    if (day < 1 || day > 31) {
      errors.push('اليوم يجب أن يكون بين 1 و 31');
      invalidFields.push('day');
    }

    if (month < 1 || month > 12) {
      errors.push('الشهر يجب أن يكون بين 1 و 12');
      invalidFields.push('month');
    }

    if (year < 1900 || year > 2100) {
      errors.push('السنة يجب أن تكون بين 1900 و 2100');
      invalidFields.push('year');
    }

    // التحقق من صحة التاريخ
    const date = new Date(year, month - 1, day);
    if (date.getDate() !== day || date.getMonth() !== month - 1 || date.getFullYear() !== year) {
      errors.push('التاريخ المدخل غير صحيح');
      invalidFields.push('day', 'month', 'year');
    }

    // التحقق من أن التاريخ ليس في المستقبل البعيد
    const today = new Date();
    const futureLimit = new Date(today.getFullYear() + 1, today.getMonth(), today.getDate());
    if (date > futureLimit) {
      errors.push('التاريخ لا يمكن أن يكون في المستقبل البعيد');
      invalidFields.push('day', 'month', 'year');
    }
  }

  // التحقق من تنسيق رقم الكتاب
  if (data.bookNumber && !/^[\d\u0660-\u0669]+[\/\-][\d\u0660-\u0669]+$/.test(data.bookNumber)) {
    errors.push('تنسيق رقم الكتاب غير صحيح (مثال: 123/2024)');
    invalidFields.push('bookNumber');
  }

  // التحقق من طول النصوص
  if (data.subject && data.subject.length > 200) {
    errors.push('الموضوع طويل جداً (الحد الأقصى 200 حرف)');
    invalidFields.push('subject');
  }

  if (data.content && data.content.length > 1000) {
    errors.push('المحتوى طويل جداً (الحد الأقصى 1000 حرف)');
    invalidFields.push('content');
  }

  return {
    isValid: errors.length === 0,
    message: errors.join('، '),
    invalidFields: [...new Set(invalidFields)]
  };
}

// وظائف عرض البيانات
function displayAllRecords() {
  const tbody = document.getElementById('reportTableBody');
  tbody.innerHTML = '';

  archiveData.forEach((record, index) => {
    const row = document.createElement('tr');
    row.innerHTML = `
      <td>${index + 1}</td>
      <td>${record.bookNumber}</td>
      <td>${record.date}</td>
      <td>${record.subject}</td>
      <td>${record.content}</td>
      <td>${record.department}</td>
      <td>${record.notes || 'لا يوجد'}</td>
    `;
    tbody.appendChild(row);
  });
}

// متغيرات الأرشيف
let currentRecordIndex = 0;
let filteredData = [];

function displayArchiveRecords(data = null) {
  filteredData = data || archiveData;
  if (filteredData.length === 0) {
    document.getElementById('recordDisplay').innerHTML = '<p>لا توجد سجلات للعرض</p>';
    return;
  }

  currentRecordIndex = 0;
  displayCurrentRecord();
  updateNavigationButtons();
}

function displayCurrentRecord() {
  if (filteredData.length === 0) return;

  const record = filteredData[currentRecordIndex];
  document.getElementById('recordDisplay').innerHTML = `
    <div class="record-card">
      <h3>سجل رقم: ${record.bookNumber}</h3>
      <div class="record-details">
        <p><strong>التاريخ:</strong> ${record.date}</p>
        <p><strong>الموضوع:</strong> ${record.subject}</p>
        <p><strong>المحتوى:</strong> ${record.content}</p>
        <p><strong>الدائرة:</strong> ${record.department}</p>
        <p><strong>الهامش:</strong> ${record.notes || 'لا يوجد'}</p>
      </div>
      <div class="record-actions">
        <button onclick="editRecord(${record.id})" class="btn btn-warning">تعديل</button>
        <button onclick="deleteRecord(${record.id})" class="btn btn-danger">حذف</button>
      </div>
    </div>
  `;
}

function updateNavigationButtons() {
  document.getElementById('firstBtn').disabled = currentRecordIndex === 0;
  document.getElementById('prevBtn').disabled = currentRecordIndex === 0;
  document.getElementById('nextBtn').disabled = currentRecordIndex === filteredData.length - 1;
  document.getElementById('lastBtn').disabled = currentRecordIndex === filteredData.length - 1;

  document.getElementById('recordCounter').textContent = 
    `${currentRecordIndex + 1} من ${filteredData.length}`;
}

// وظائف التنقل في الأرشيف
function firstRecord() {
  currentRecordIndex = 0;
  displayCurrentRecord();
  updateNavigationButtons();
}

function prevRecord() {
  if (currentRecordIndex > 0) {
    currentRecordIndex--;
    displayCurrentRecord();
    updateNavigationButtons();
  }
}

function nextRecord() {
  if (currentRecordIndex < filteredData.length - 1) {
    currentRecordIndex++;
    displayCurrentRecord();
    updateNavigationButtons();
  }
}

function lastRecord() {
  currentRecordIndex = filteredData.length - 1;
  displayCurrentRecord();
  updateNavigationButtons();
}

// تعديل وحذف السجلات
function editRecord(id) {
  const record = archiveData.find(r => r.id === id);
  if (!record) return;

  // ملء النموذج بالبيانات الحالية
  document.getElementById('bookNumber').value = record.bookNumber;
  document.getElementById('day').value = record.day;
  document.getElementById('month').value = record.month;
  document.getElementById('year').value = record.year;
  document.getElementById('subject').value = record.subject;
  document.getElementById('content').value = record.content;
  document.getElementById('department').value = record.department;
  document.getElementById('notes').value = record.notes;

  // حذف السجل القديم
  archiveData = archiveData.filter(r => r.id !== id);
  saveData();

  // الانتقال إلى صفحة إدخال البيانات
  showPage('dataEntryPage');
  showNotification('يمكنك الآن تعديل البيانات والحفظ', 'info');
}

function deleteRecord(id) {
  if (confirm('هل أنت متأكد من حذف هذا السجل؟')) {
    archiveData = archiveData.filter(r => r.id !== id);
    saveData();
    displayArchiveRecords();
    showNotification('تم حذف السجل بنجاح', 'success');
    playSound('delete');
  }
}

// وظائف التصفية المحسنة
function applyFilter() {
  const filters = {
    fromDay: document.getElementById('fromDay').value,
    fromMonth: document.getElementById('fromMonth').value,
    fromYear: document.getElementById('fromYear').value,
    toDay: document.getElementById('toDay').value,
    toMonth: document.getElementById('toMonth').value,
    toYear: document.getElementById('toYear').value,
    subject: document.getElementById('subjectFilter').value.toLowerCase().trim(),
    employee: document.getElementById('employeeFilter').value.toLowerCase().trim(),
    department: document.getElementById('departmentFilter').value.toLowerCase().trim(),
    notes: document.getElementById('notesFilter').value,
    bookNumber: document.getElementById('bookNumberFilter')?.value.toLowerCase().trim() || ''
  };

  // التحقق من صحة التواريخ
  if (filters.fromDay && filters.fromMonth && filters.fromYear &&
      filters.toDay && filters.toMonth && filters.toYear) {
    const fromDate = new Date(filters.fromYear, filters.fromMonth - 1, filters.fromDay);
    const toDate = new Date(filters.toYear, filters.toMonth - 1, filters.toDay);
    if (fromDate > toDate) {
      showNotification('تاريخ البداية يجب أن يكون قبل تاريخ النهاية', 'error');
      return;
    }
  }

  let filtered = archiveData.filter(record => {
    // تصفية التاريخ المحسنة
    if (filters.fromDay && filters.fromMonth && filters.fromYear) {
      const fromDate = new Date(filters.fromYear, filters.fromMonth - 1, filters.fromDay);
      const recordDate = new Date(record.year, record.month - 1, record.day);
      if (recordDate < fromDate) return false;
    }

    if (filters.toDay && filters.toMonth && filters.toYear) {
      const toDate = new Date(filters.toYear, filters.toMonth - 1, filters.toDay);
      toDate.setHours(23, 59, 59, 999); // نهاية اليوم
      const recordDate = new Date(record.year, record.month - 1, record.day);
      if (recordDate > toDate) return false;
    }

    // تصفية رقم الكتاب
    if (filters.bookNumber && !record.bookNumber.toLowerCase().includes(filters.bookNumber)) {
      return false;
    }

    // تصفية الموضوع مع دعم البحث الضبابي
    if (filters.subject) {
      const subjectMatch = record.subject.toLowerCase().includes(filters.subject) ||
                          fuzzySearch(record.subject.toLowerCase(), filters.subject);
      if (!subjectMatch) return false;
    }

    // تصفية الموظف مع دعم البحث المتقدم
    if (filters.employee) {
      const contentMatch = record.content.toLowerCase().includes(filters.employee) ||
                          fuzzySearch(record.content.toLowerCase(), filters.employee);
      if (!contentMatch) return false;
    }

    // تصفية الدائرة مع دعم البحث الجزئي
    if (filters.department) {
      const deptMatch = record.department.toLowerCase().includes(filters.department) ||
                       fuzzySearch(record.department.toLowerCase(), filters.department);
      if (!deptMatch) return false;
    }

    // تصفية الهامش المحسنة
    if (filters.notes === 'has-notes' && (!record.notes || record.notes.trim() === '')) {
      return false;
    }
    if (filters.notes === 'no-notes' && record.notes && record.notes.trim() !== '') {
      return false;
    }
    if (filters.notes === 'important-notes' && (!record.notes || !record.notes.includes('مهم'))) {
      return false;
    }

    return true;
  });

  // ترتيب النتائج حسب الصلة
  filtered = sortByRelevance(filtered, filters);

  displayArchiveRecords(filtered);
  showPage('archivePage');

  // إحصائيات مفصلة
  const stats = generateFilterStats(filtered, archiveData.length);
  showNotification(`${stats.message}`, 'info', 5000);

  // حفظ معايير التصفية
  saveFilterCriteria(filters);
}

function clearFilter() {
  document.getElementById('filterForm').reset();
  displayArchiveRecords();
  showNotification('تم إلغاء التصفية', 'info');

  // مسح معايير التصفية المحفوظة
  localStorage.removeItem('lastFilterCriteria');
}

// ترتيب النتائج حسب الصلة
function sortByRelevance(records, filters) {
  return records.sort((a, b) => {
    let scoreA = 0, scoreB = 0;

    // نقاط إضافية للتطابق الدقيق
    if (filters.subject && a.subject.toLowerCase().includes(filters.subject)) scoreA += 10;
    if (filters.subject && b.subject.toLowerCase().includes(filters.subject)) scoreB += 10;

    if (filters.employee && a.content.toLowerCase().includes(filters.employee)) scoreA += 8;
    if (filters.employee && b.content.toLowerCase().includes(filters.employee)) scoreB += 8;

    if (filters.department && a.department.toLowerCase().includes(filters.department)) scoreA += 6;
    if (filters.department && b.department.toLowerCase().includes(filters.department)) scoreB += 6;

    // ترتيب حسب التاريخ (الأحدث أولاً) إذا كانت النقاط متساوية
    if (scoreA === scoreB) {
      const dateA = new Date(a.year, a.month - 1, a.day);
      const dateB = new Date(b.year, b.month - 1, b.day);
      return dateB - dateA;
    }

    return scoreB - scoreA;
  });
}

// إنشاء إحصائيات التصفية
function generateFilterStats(filtered, total) {
  const percentage = total > 0 ? Math.round((filtered.length / total) * 100) : 0;
  const message = `تم العثور على ${filtered.length} سجل من أصل ${total} (${percentage}%)`;

  return {
    count: filtered.length,
    total: total,
    percentage: percentage,
    message: message
  };
}

// حفظ معايير التصفية
function saveFilterCriteria(filters) {
  const nonEmptyFilters = {};
  for (const [key, value] of Object.entries(filters)) {
    if (value && value !== '') {
      nonEmptyFilters[key] = value;
    }
  }

  if (Object.keys(nonEmptyFilters).length > 0) {
    localStorage.setItem('lastFilterCriteria', JSON.stringify(nonEmptyFilters));
  }
}

// استعادة معايير التصفية المحفوظة
function restoreFilterCriteria() {
  const saved = localStorage.getItem('lastFilterCriteria');
  if (saved) {
    try {
      const filters = JSON.parse(saved);
      for (const [key, value] of Object.entries(filters)) {
        const element = document.getElementById(key === 'subject' ? 'subjectFilter' :
                                             key === 'employee' ? 'employeeFilter' :
                                             key === 'department' ? 'departmentFilter' :
                                             key === 'notes' ? 'notesFilter' :
                                             key === 'bookNumber' ? 'bookNumberFilter' :
                                             key);
        if (element) {
          element.value = value;
        }
      }
      showNotification('تم استعادة معايير التصفية السابقة', 'info');
    } catch (error) {
      console.error('خطأ في استعادة معايير التصفية:', error);
    }
  }
}

// وظائف الثيمات
const themes = {
  default: {
    primary: '#3498db',
    secondary: '#2ecc71',
    accent: '#e74c3c',
    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    text: '#2c3e50'
  },
  ocean: {
    primary: '#1abc9c',
    secondary: '#3498db',
    accent: '#e67e22',
    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    text: '#2c3e50'
  },
  sunset: {
    primary: '#e74c3c',
    secondary: '#f39c12',
    accent: '#9b59b6',
    background: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
    text: '#2c3e50'
  },
  forest: {
    primary: '#27ae60',
    secondary: '#2ecc71',
    accent: '#f39c12',
    background: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
    text: '#2c3e50'
  }
};

function applyTheme(themeName) {
  const theme = themes[themeName];
  if (!theme) return;

  const root = document.documentElement;
  root.style.setProperty('--primary-color', theme.primary);
  root.style.setProperty('--secondary-color', theme.secondary);
  root.style.setProperty('--accent-color', theme.accent);
  root.style.setProperty('--background-gradient', theme.background);
  root.style.setProperty('--text-color', theme.text);

  currentTheme = themeName;
  localStorage.setItem('currentTheme', currentTheme);

  showNotification('تم تطبيق الثيم بنجاح', 'success');
}

// وظائف مساعدة محسنة للتوافق
function saveData() {
  return new Promise(function(resolve, reject) {
    try {
      // فحص دعم localStorage
      if (typeof localStorage !== 'undefined' && localStorage.setItem) {
        localStorage.setItem('archiveData', JSON.stringify(archiveData));
      } else {
        // استخدام cookies كبديل للمتصفحات القديمة
        setCookie('archiveData', JSON.stringify(archiveData), 365);
      }

      // حفظ البيانات باستخدام واجهة Electron إذا كانت متوفرة
      if (window.electronAPI && window.electronAPI.saveData) {
        window.electronAPI.saveData(archiveData).then(function() {
          resolve(true);
        }).catch(function(error) {
          console.warn('خطأ في حفظ البيانات عبر Electron:', error);
          resolve(true); // نجح الحفظ المحلي على الأقل
        });
      } else {
        resolve(true);
      }
    } catch (error) {
      console.error('خطأ في حفظ البيانات:', error);
      showNotification('خطأ في حفظ البيانات', 'error');
      reject(error);
    }
  });
}

// دوال مساعدة للتعامل مع cookies (بديل localStorage)
function setCookie(name, value, days) {
  var expires = "";
  if (days) {
    var date = new Date();
    date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
    expires = "; expires=" + date.toUTCString();
  }
  document.cookie = name + "=" + (value || "") + expires + "; path=/";
}

function getCookie(name) {
  var nameEQ = name + "=";
  var ca = document.cookie.split(';');
  for (var i = 0; i < ca.length; i++) {
    var c = ca[i];
    while (c.charAt(0) === ' ') c = c.substring(1, c.length);
    if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
  }
  return null;
}

function eraseCookie(name) {
  document.cookie = name + '=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;';
}

function showNotification(message, type = 'info', duration = 3000) {
  const notification = document.createElement('div');
  notification.className = `notification notification-${type}`;

  // إضافة أيقونة حسب نوع التنبيه
  const icon = getNotificationIcon(type);
  notification.innerHTML = `
    <div class="notification-content">
      <span class="notification-icon">${icon}</span>
      <span class="notification-message">${message}</span>
      <button class="notification-close" onclick="closeNotification(this)">&times;</button>
    </div>
  `;

  document.body.appendChild(notification);

  setTimeout(() => {
    notification.classList.add('show');
  }, 100);

  // إزالة التنبيه تلقائياً
  const autoRemove = setTimeout(() => {
    removeNotification(notification);
  }, duration);

  // حفظ معرف المؤقت لإمكانية إلغائه
  notification.dataset.timerId = autoRemove;
}

// الحصول على أيقونة التنبيه
function getNotificationIcon(type) {
  const icons = {
    'success': '✅',
    'error': '❌',
    'warning': '⚠️',
    'info': 'ℹ️'
  };
  return icons[type] || icons.info;
}

// إغلاق التنبيه يدوياً
function closeNotification(button) {
  const notification = button.closest('.notification');
  if (notification) {
    // إلغاء المؤقت التلقائي
    const timerId = notification.dataset.timerId;
    if (timerId) {
      clearTimeout(parseInt(timerId));
    }
    removeNotification(notification);
  }
}

// إزالة التنبيه مع التأثير
function removeNotification(notification) {
  if (notification && notification.parentNode) {
    notification.classList.remove('show');
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 300);
  }
}

function playSound(type) {
  // تشغيل الأصوات مع دعم متعدد الصيغ
  const soundEnabled = localStorage.getItem('soundEnabled') !== 'false';
  if (!soundEnabled) return;

  try {
    // محاولة تشغيل صيغ مختلفة من الصوت
    const formats = ['mp3', 'wav', 'ogg'];
    const audio = new Audio();

    for (const format of formats) {
      if (audio.canPlayType(`audio/${format}`)) {
        audio.src = `sounds/${type}.${format}`;
        break;
      }
    }

    audio.volume = parseFloat(localStorage.getItem('soundVolume') || '0.3');
    audio.play().catch(() => {
      // تجاهل الأخطاء إذا لم تكن الأصوات متوفرة
    });
  } catch (error) {
    // تجاهل أخطاء الصوت
  }
}

// حفظ سجل البحث
function saveSearchHistory(query, resultsCount) {
  const history = JSON.parse(localStorage.getItem('searchHistory') || '[]');
  const entry = {
    id: Date.now(),
    query: query,
    resultsCount: resultsCount,
    timestamp: new Date().toISOString()
  };

  // إضافة البحث في المقدمة
  history.unshift(entry);

  // الاحتفاظ بآخر 50 بحث فقط
  if (history.length > 50) {
    history.splice(50);
  }

  localStorage.setItem('searchHistory', JSON.stringify(history));
}

// عرض سجل البحث
function showSearchHistory() {
  const history = JSON.parse(localStorage.getItem('searchHistory') || '[]');
  if (history.length === 0) {
    showNotification('لا يوجد سجل بحث', 'info');
    return;
  }

  const modal = createSearchHistoryModal(history);
  document.body.appendChild(modal);
  modal.style.display = 'flex';
}

// إنشاء نافذة سجل البحث
function createSearchHistoryModal(history) {
  const modal = document.createElement('div');
  modal.className = 'modal-overlay';
  modal.innerHTML = `
    <div class="modal-content search-history-modal">
      <div class="modal-header">
        <h3>سجل البحث</h3>
        <button class="modal-close" onclick="closeModal(this)">&times;</button>
      </div>
      <div class="modal-body">
        <div class="search-history-list">
          ${history.map(entry => `
            <div class="search-history-item" onclick="repeatSearch('${entry.query}')">
              <div class="search-query">${entry.query}</div>
              <div class="search-info">
                <span class="results-count">${entry.resultsCount} نتيجة</span>
                <span class="search-date">${formatDate(new Date(entry.timestamp))}</span>
              </div>
            </div>
          `).join('')}
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn btn-secondary" onclick="clearSearchHistory()">مسح السجل</button>
        <button class="btn btn-primary" onclick="closeModal(this)">إغلاق</button>
      </div>
    </div>
  `;
  return modal;
}

// تصدير البيانات المحسن
async function exportData() {
  try {
    if (archiveData.length === 0) {
      showNotification('لا توجد بيانات للتصدير', 'warning');
      return;
    }

    const stats = generateDetailedStatistics();
    const dataToExport = {
      version: '2.0',
      exportDate: new Date().toISOString(),
      totalRecords: archiveData.length,
      statistics: stats,
      metadata: {
        exportedBy: 'برنامج الأرشفة الإلكترونية',
        appVersion: '1.0.0',
        dataIntegrity: calculateDataHash(archiveData)
      },
      data: archiveData
    };

    // استخدام واجهة Electron للتصدير إذا كانت متوفرة
    if (window.electronAPI) {
      await window.electronAPI.exportData(dataToExport);
      showNotification('تم تصدير البيانات بنجاح', 'success');
    } else {
      // استخدام طريقة التصدير التقليدية للمتصفح
      const dataStr = JSON.stringify(dataToExport, null, 2);
      const blob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(blob);

      const a = document.createElement('a');
      a.href = url;
      a.download = `archive-backup-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);

      URL.revokeObjectURL(url);
      showNotification('تم تصدير البيانات بنجاح', 'success');
    }

    // تسجيل العملية
    logActivity('export', null, `تم تصدير ${archiveData.length} سجل`);
    playSound('success');
  } catch (error) {
    console.error('خطأ في تصدير البيانات:', error);
    showNotification('خطأ في تصدير البيانات: ' + error.message, 'error');
  }
}

// حساب hash للبيانات للتحقق من سلامتها
function calculateDataHash(data) {
  const str = JSON.stringify(data);
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32bit integer
  }
  return hash.toString(16);
}

// استيراد البيانات المحسن
async function importData(event) {
  try {
    // استخدام واجهة Electron للاستيراد إذا كانت متوفرة
    if (window.electronAPI) {
      const importedData = await window.electronAPI.importData();
      processImportedData(importedData);
    } else {
      // استخدام طريقة الاستيراد التقليدية للمتصفح
      const file = event.target.files[0];
      if (!file) return;

      const reader = new FileReader();
      reader.onload = function(e) {
        try {
          const importedData = JSON.parse(e.target.result);
          processImportedData(importedData);
        } catch (error) {
          showNotification('خطأ في قراءة الملف: ' + error.message, 'error');
        }
      };
      reader.readAsText(file);
    }
  } catch (error) {
    console.error('خطأ في استيراد البيانات:', error);
    showNotification('خطأ في استيراد البيانات: ' + error.message, 'error');
  }
}

// معالجة البيانات المستوردة
function processImportedData(importedData) {
  try {
    let dataArray = [];

    // التحقق من تنسيق البيانات
    if (Array.isArray(importedData)) {
      // تنسيق قديم - مصفوفة مباشرة
      dataArray = importedData;
    } else if (importedData && importedData.data && Array.isArray(importedData.data)) {
      // تنسيق جديد - كائن يحتوي على البيانات والمعلومات الإضافية
      dataArray = importedData.data;

      // التحقق من سلامة البيانات إذا كان متوفراً
      if (importedData.metadata && importedData.metadata.dataIntegrity) {
        const currentHash = calculateDataHash(dataArray);
        if (currentHash !== importedData.metadata.dataIntegrity) {
          showNotification('تحذير: قد تكون البيانات المستوردة تالفة', 'warning');
        }
      }

      // عرض معلومات الاستيراد
      if (importedData.statistics) {
        showNotification(`تم استيراد ${dataArray.length} سجل من ملف تم تصديره في ${new Date(importedData.exportDate).toLocaleDateString('ar-SA')}`, 'info', 5000);
      }
    } else {
      throw new Error('تنسيق الملف غير مدعوم');
    }

    // التحقق من صحة البيانات
    const validationResult = validateImportedData(dataArray);
    if (!validationResult.isValid) {
      showNotification(`خطأ في البيانات: ${validationResult.message}`, 'error');
      return;
    }

    // تأكيد الاستيراد إذا كانت هناك بيانات موجودة
    if (archiveData.length > 0) {
      const confirmed = confirm(`سيتم استبدال ${archiveData.length} سجل موجود بـ ${dataArray.length} سجل جديد. هل تريد المتابعة؟`);
      if (!confirmed) return;
    }

    // حفظ نسخة احتياطية من البيانات الحالية
    if (archiveData.length > 0) {
      localStorage.setItem('archiveDataBackup', JSON.stringify(archiveData));
    }

    // استيراد البيانات
    archiveData = dataArray;
    saveData();
    updateStatistics();

    showNotification(`تم استيراد ${dataArray.length} سجل بنجاح`, 'success');
    playSound('success');

    // تسجيل العملية
    logActivity('import', null, `تم استيراد ${dataArray.length} سجل`);

    // إعادة تحميل الصفحة الحالية لعرض البيانات الجديدة
    if (document.getElementById('archivePage').style.display !== 'none') {
      displayArchiveRecords();
    }

  } catch (error) {
    console.error('خطأ في معالجة البيانات المستوردة:', error);
    showNotification('خطأ في معالجة البيانات: ' + error.message, 'error');
  }
}

// التحقق من صحة البيانات المستوردة
function validateImportedData(data) {
  if (!Array.isArray(data)) {
    return { isValid: false, message: 'البيانات يجب أن تكون مصفوفة' };
  }

  if (data.length === 0) {
    return { isValid: false, message: 'لا توجد سجلات للاستيراد' };
  }

  // التحقق من الحقول المطلوبة في كل سجل
  const requiredFields = ['bookNumber', 'day', 'month', 'year', 'subject', 'content', 'department'];

  for (let i = 0; i < data.length; i++) {
    const record = data[i];

    for (const field of requiredFields) {
      if (!record.hasOwnProperty(field) || record[field] === null || record[field] === undefined) {
        return { isValid: false, message: `السجل رقم ${i + 1} يفتقد للحقل المطلوب: ${field}` };
      }
    }

    // التحقق من صحة التاريخ
    const day = parseInt(record.day);
    const month = parseInt(record.month);
    const year = parseInt(record.year);

    if (isNaN(day) || isNaN(month) || isNaN(year) ||
        day < 1 || day > 31 || month < 1 || month > 12 || year < 1900 || year > 2100) {
      return { isValid: false, message: `السجل رقم ${i + 1} يحتوي على تاريخ غير صحيح` };
    }
  }

  return { isValid: true, message: 'البيانات صحيحة' };
}

// البحث السريع المحسن مع خيارات متقدمة
function quickSearch() {
  const searchTerm = document.getElementById('quickSearchInput').value.toLowerCase();
  if (!searchTerm) {
    displayArchiveRecords();
    return;
  }

  // البحث المتقدم مع دعم البحث الضبابي والكلمات المفتاحية المتعددة
  const keywords = searchTerm.split(/\s+/).filter(keyword => keyword.length > 0);

  const filtered = archiveData.filter(record => {
    const searchableText = [
      record.bookNumber,
      record.subject,
      record.content,
      record.department,
      record.notes || ''
    ].join(' ').toLowerCase();

    // البحث بالكلمات المفتاحية المتعددة
    return keywords.every(keyword => searchableText.includes(keyword)) ||
           // البحث الضبابي للكلمات المشابهة
           keywords.some(keyword => fuzzySearch(searchableText, keyword));
  });

  displayArchiveRecords(filtered);
  showNotification(`تم العثور على ${filtered.length} نتيجة من أصل ${archiveData.length}`, 'info');

  // حفظ البحث في السجل
  saveSearchHistory(searchTerm, filtered.length);
}

// البحث الضبابي للكلمات المشابهة
function fuzzySearch(text, pattern) {
  const threshold = 0.8; // نسبة التشابه المطلوبة
  const words = text.split(/\s+/);

  return words.some(word => {
    if (word.length < 3 || pattern.length < 3) return false;
    const similarity = calculateSimilarity(word, pattern);
    return similarity >= threshold;
  });
}

// حساب نسبة التشابه بين كلمتين
function calculateSimilarity(str1, str2) {
  const longer = str1.length > str2.length ? str1 : str2;
  const shorter = str1.length > str2.length ? str2 : str1;

  if (longer.length === 0) return 1.0;

  const editDistance = levenshteinDistance(longer, shorter);
  return (longer.length - editDistance) / longer.length;
}

// حساب المسافة بين الكلمات (Levenshtein Distance)
function levenshteinDistance(str1, str2) {
  const matrix = [];

  for (let i = 0; i <= str2.length; i++) {
    matrix[i] = [i];
  }

  for (let j = 0; j <= str1.length; j++) {
    matrix[0][j] = j;
  }

  for (let i = 1; i <= str2.length; i++) {
    for (let j = 1; j <= str1.length; j++) {
      if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
        matrix[i][j] = matrix[i - 1][j - 1];
      } else {
        matrix[i][j] = Math.min(
          matrix[i - 1][j - 1] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j] + 1
        );
      }
    }
  }

  return matrix[str2.length][str1.length];
}

// إعادة تشغيل البحث من السجل
function repeatSearch(query) {
  document.getElementById('quickSearchInput').value = query;
  quickSearch();
  closeModal(document.querySelector('.search-history-modal'));
}

// مسح سجل البحث
function clearSearchHistory() {
  if (confirm('هل أنت متأكد من مسح سجل البحث؟')) {
    localStorage.removeItem('searchHistory');
    showNotification('تم مسح سجل البحث', 'success');
    closeModal(document.querySelector('.search-history-modal'));
  }
}

// إغلاق النافذة المنبثقة
function closeModal(modal) {
  if (modal) {
    modal.style.display = 'none';
    setTimeout(() => {
      if (modal.parentNode) {
        modal.parentNode.removeChild(modal);
      }
    }, 300);
  }
}

// تنسيق التاريخ
function formatDate(date) {
  return date.toLocaleDateString('ar-SA', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
}

// توليد معرف فريد
function generateUniqueId() {
  return Date.now() + Math.random().toString(36).substr(2, 9);
}

// تمييز الحقول غير الصحيحة
function highlightInvalidFields(fields) {
  // مسح التمييز السابق
  clearFieldHighlights();

  fields.forEach(fieldName => {
    const field = document.getElementById(fieldName);
    if (field) {
      field.classList.add('invalid-field');
      field.addEventListener('input', clearSingleFieldHighlight, { once: true });
    }
  });
}

// مسح تمييز الحقول
function clearFieldHighlights() {
  const invalidFields = document.querySelectorAll('.invalid-field');
  invalidFields.forEach(field => {
    field.classList.remove('invalid-field');
  });
}

// مسح تمييز حقل واحد
function clearSingleFieldHighlight(event) {
  event.target.classList.remove('invalid-field');
}

// تسجيل العمليات
function logActivity(action, recordId, description) {
  const activities = JSON.parse(localStorage.getItem('activityLog') || '[]');
  const activity = {
    id: generateUniqueId(),
    action: action,
    recordId: recordId,
    description: description,
    timestamp: new Date().toISOString(),
    user: 'المستخدم الحالي'
  };

  activities.unshift(activity);

  // الاحتفاظ بآخر 100 عملية فقط
  if (activities.length > 100) {
    activities.splice(100);
  }

  localStorage.setItem('activityLog', JSON.stringify(activities));
}

// عرض سجل العمليات
function showActivityLog() {
  const activities = JSON.parse(localStorage.getItem('activityLog') || '[]');
  if (activities.length === 0) {
    showNotification('لا يوجد سجل عمليات', 'info');
    return;
  }

  const modal = createActivityLogModal(activities);
  document.body.appendChild(modal);
  modal.style.display = 'flex';
}

// إنشاء نافذة سجل العمليات
function createActivityLogModal(activities) {
  const modal = document.createElement('div');
  modal.className = 'modal-overlay';
  modal.innerHTML = `
    <div class="modal-content activity-log-modal">
      <div class="modal-header">
        <h3>سجل العمليات</h3>
        <button class="modal-close" onclick="closeModal(this.closest('.modal-overlay'))">&times;</button>
      </div>
      <div class="modal-body">
        <div class="activity-log-list">
          ${activities.map(activity => `
            <div class="activity-item">
              <div class="activity-description">${activity.description}</div>
              <div class="activity-info">
                <span class="activity-action">${getActionText(activity.action)}</span>
                <span class="activity-date">${formatDate(new Date(activity.timestamp))}</span>
              </div>
            </div>
          `).join('')}
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn btn-secondary" onclick="clearActivityLog()">مسح السجل</button>
        <button class="btn btn-primary" onclick="closeModal(this.closest('.modal-overlay'))">إغلاق</button>
      </div>
    </div>
  `;
  return modal;
}

// الحصول على نص العملية
function getActionText(action) {
  const actions = {
    'add': 'إضافة',
    'edit': 'تعديل',
    'delete': 'حذف',
    'search': 'بحث',
    'filter': 'تصفية',
    'export': 'تصدير',
    'import': 'استيراد'
  };
  return actions[action] || action;
}

// مسح سجل العمليات
function clearActivityLog() {
  if (confirm('هل أنت متأكد من مسح سجل العمليات؟')) {
    localStorage.removeItem('activityLog');
    showNotification('تم مسح سجل العمليات', 'success');
    closeModal(document.querySelector('.activity-log-modal'));
  }
}

// تحديث الإحصائيات المحسنة
function updateStatistics() {
  const stats = generateDetailedStatistics();

  // تحديث عناصر الإحصائيات في الصفحة الرئيسية
  const totalElement = document.getElementById('totalRecordsCount');
  const todayElement = document.getElementById('todayRecordsCount');
  const deptElement = document.getElementById('departmentsCount');

  if (totalElement) totalElement.textContent = stats.totalRecords;
  if (todayElement) todayElement.textContent = stats.todayRecords;
  if (deptElement) deptElement.textContent = stats.departmentsCount;

  // تحديث معلومات البرنامج
  const dataSize = JSON.stringify(archiveData).length;
  const dbSizeElement = document.getElementById('dbSize');
  if (dbSizeElement) {
    dbSizeElement.textContent = formatSize(dataSize);
  }

  // تحديث الرسوم البيانية إذا كانت موجودة
  updateCharts(stats);
}

// إنشاء إحصائيات مفصلة
function generateDetailedStatistics() {
  const today = new Date();
  const thisMonth = new Date(today.getFullYear(), today.getMonth(), 1);
  const thisYear = new Date(today.getFullYear(), 0, 1);

  const stats = {
    totalRecords: archiveData.length,
    todayRecords: 0,
    thisMonthRecords: 0,
    thisYearRecords: 0,
    departmentsCount: 0,
    departmentStats: {},
    monthlyStats: {},
    yearlyStats: {},
    recordsWithNotes: 0,
    averageRecordsPerDay: 0,
    mostActiveDepartment: '',
    oldestRecord: null,
    newestRecord: null
  };

  if (archiveData.length === 0) return stats;

  // تحليل السجلات
  archiveData.forEach(record => {
    const recordDate = new Date(record.year, record.month - 1, record.day);

    // إحصائيات التاريخ
    if (recordDate.toDateString() === today.toDateString()) {
      stats.todayRecords++;
    }

    if (recordDate >= thisMonth) {
      stats.thisMonthRecords++;
    }

    if (recordDate >= thisYear) {
      stats.thisYearRecords++;
    }

    // إحصائيات الدوائر
    if (record.department) {
      stats.departmentStats[record.department] = (stats.departmentStats[record.department] || 0) + 1;
    }

    // إحصائيات شهرية
    const monthKey = `${record.year}-${record.month.toString().padStart(2, '0')}`;
    stats.monthlyStats[monthKey] = (stats.monthlyStats[monthKey] || 0) + 1;

    // إحصائيات سنوية
    stats.yearlyStats[record.year] = (stats.yearlyStats[record.year] || 0) + 1;

    // السجلات مع الهوامش
    if (record.notes && record.notes.trim()) {
      stats.recordsWithNotes++;
    }

    // أقدم وأحدث سجل
    if (!stats.oldestRecord || recordDate < new Date(stats.oldestRecord.year, stats.oldestRecord.month - 1, stats.oldestRecord.day)) {
      stats.oldestRecord = record;
    }

    if (!stats.newestRecord || recordDate > new Date(stats.newestRecord.year, stats.newestRecord.month - 1, stats.newestRecord.day)) {
      stats.newestRecord = record;
    }
  });

  // حساب الإحصائيات المشتقة
  stats.departmentsCount = Object.keys(stats.departmentStats).length;

  // أكثر دائرة نشاطاً
  let maxCount = 0;
  for (const [dept, count] of Object.entries(stats.departmentStats)) {
    if (count > maxCount) {
      maxCount = count;
      stats.mostActiveDepartment = dept;
    }
  }

  // متوسط السجلات يومياً
  if (stats.oldestRecord && stats.newestRecord) {
    const oldestDate = new Date(stats.oldestRecord.year, stats.oldestRecord.month - 1, stats.oldestRecord.day);
    const newestDate = new Date(stats.newestRecord.year, stats.newestRecord.month - 1, stats.newestRecord.day);
    const daysDiff = Math.max(1, Math.ceil((newestDate - oldestDate) / (1000 * 60 * 60 * 24)));
    stats.averageRecordsPerDay = Math.round((stats.totalRecords / daysDiff) * 100) / 100;
  }

  return stats;
}

// عرض الإحصائيات التفصيلية
function showDetailedStatistics() {
  const stats = generateDetailedStatistics();
  const modal = createStatisticsModal(stats);
  document.body.appendChild(modal);
  modal.style.display = 'flex';
}

// إنشاء نافذة الإحصائيات
function createStatisticsModal(stats) {
  const modal = document.createElement('div');
  modal.className = 'modal-overlay';
  modal.innerHTML = `
    <div class="modal-content statistics-modal">
      <div class="modal-header">
        <h3>الإحصائيات التفصيلية</h3>
        <button class="modal-close" onclick="closeModal(this.closest('.modal-overlay'))">&times;</button>
      </div>
      <div class="modal-body">
        <div class="statistics-grid">
          <div class="stat-card">
            <h4>إحصائيات عامة</h4>
            <div class="stat-item">
              <span class="stat-label">إجمالي السجلات:</span>
              <span class="stat-value">${stats.totalRecords}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">سجلات اليوم:</span>
              <span class="stat-value">${stats.todayRecords}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">سجلات هذا الشهر:</span>
              <span class="stat-value">${stats.thisMonthRecords}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">سجلات هذا العام:</span>
              <span class="stat-value">${stats.thisYearRecords}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">عدد الدوائر:</span>
              <span class="stat-value">${stats.departmentsCount}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">السجلات مع هوامش:</span>
              <span class="stat-value">${stats.recordsWithNotes}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">متوسط السجلات يومياً:</span>
              <span class="stat-value">${stats.averageRecordsPerDay}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">أكثر دائرة نشاطاً:</span>
              <span class="stat-value">${stats.mostActiveDepartment}</span>
            </div>
          </div>

          <div class="stat-card">
            <h4>إحصائيات الدوائر</h4>
            <div class="department-stats">
              ${Object.entries(stats.departmentStats)
                .sort(([,a], [,b]) => b - a)
                .map(([dept, count]) => `
                  <div class="stat-item">
                    <span class="stat-label">${dept}:</span>
                    <span class="stat-value">${count}</span>
                  </div>
                `).join('')}
            </div>
          </div>

          <div class="stat-card">
            <h4>إحصائيات سنوية</h4>
            <div class="yearly-stats">
              ${Object.entries(stats.yearlyStats)
                .sort(([a], [b]) => b - a)
                .map(([year, count]) => `
                  <div class="stat-item">
                    <span class="stat-label">${year}:</span>
                    <span class="stat-value">${count}</span>
                  </div>
                `).join('')}
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn btn-secondary" onclick="exportStatistics()">تصدير الإحصائيات</button>
        <button class="btn btn-primary" onclick="closeModal(this.closest('.modal-overlay'))">إغلاق</button>
      </div>
    </div>
  `;
  return modal;
}

// تصدير الإحصائيات
function exportStatistics() {
  const stats = generateDetailedStatistics();
  const data = {
    timestamp: new Date().toISOString(),
    statistics: stats,
    generatedBy: 'برنامج الأرشفة الإلكترونية'
  };

  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `statistics_${new Date().toISOString().split('T')[0]}.json`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);

  showNotification('تم تصدير الإحصائيات بنجاح', 'success');
}

// تحديث الرسوم البيانية (placeholder)
function updateCharts(stats) {
  // يمكن إضافة مكتبة رسوم بيانية هنا مثل Chart.js
  console.log('تحديث الرسوم البيانية:', stats);
}

// تنسيق حجم البيانات
function formatSize(bytes) {
  if (bytes === 0) return '0 بايت';
  const k = 1024;
  const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// إضافة مستمعي الأحداث للاختصارات
document.addEventListener('keydown', function(event) {
  // Ctrl + S للحفظ السريع
  if (event.ctrlKey && event.key === 's') {
    event.preventDefault();
    if (document.getElementById('dataEntryPage').style.display !== 'none') {
      addRecord();
    }
  }

  // Ctrl + F للتركيز على البحث السريع
  if (event.ctrlKey && event.key === 'f') {
    event.preventDefault();
    const searchInput = document.getElementById('quickSearchInput');
    if (searchInput) {
      searchInput.focus();
    }
  }

  // Escape للعودة للصفحة الرئيسية
  if (event.key === 'Escape') {
    showPage('welcomePage');
  }

  // أسهم التنقل في الأرشيف
  if (document.getElementById('archivePage').style.display !== 'none') {
    if (event.key === 'ArrowLeft') {
      event.preventDefault();
      nextRecord();
    } else if (event.key === 'ArrowRight') {
      event.preventDefault();
      prevRecord();
    }
  }
});

// تحسين دالة البحث السريع لتعمل مع Enter
document.addEventListener('DOMContentLoaded', function() {
  const quickSearchInput = document.getElementById('quickSearchInput');
  if (quickSearchInput) {
    quickSearchInput.addEventListener('keypress', function(event) {
      if (event.key === 'Enter') {
        quickSearch();
      }
    });
  }
});

// دالة لإظهار نصائح الاستخدام
function showUsageTips() {
  const tips = [
    'استخدم Ctrl + S للحفظ السريع في صفحة إدخال البيانات',
    'استخدم Ctrl + F للتركيز على حقل البحث السريع',
    'اضغط Escape للعودة إلى الصفحة الرئيسية',
    'استخدم الأسهم ← → للتنقل في الأرشيف',
    'يمكنك البحث بعدة كلمات مفصولة بمسافات',
    'البحث الضبابي يساعدك في العثور على النتائج حتى مع الأخطاء الإملائية',
    'استخدم سجل البحث لإعادة تشغيل عمليات البحث السابقة',
    'تحقق من الإحصائيات التفصيلية لمعرفة المزيد عن بياناتك'
  ];

  const randomTip = tips[Math.floor(Math.random() * tips.length)];
  showNotification(`💡 نصيحة: ${randomTip}`, 'info', 7000);
}

// عرض نصيحة عشوائية عند تحميل الصفحة
setTimeout(showUsageTips, 3000);

// تحسينات إضافية للتوافق مع جميع الأنظمة

// فحص وتحسين الأداء حسب الجهاز
function optimizeForDevice() {
  var deviceInfo = getDeviceInfo();

  // تحسينات للأجهزة الضعيفة
  if (deviceInfo.isLowEnd) {
    // تقليل التأثيرات البصرية
    document.body.classList.add('low-performance');

    // تقليل عدد العناصر المعروضة
    var maxRecordsPerPage = 20;

    // إيقاف الأصوات
    localStorage.setItem('soundEnabled', 'false');

    showNotification('تم تحسين التطبيق للجهاز الحالي', 'info');
  }

  // تحسينات للشاشات الصغيرة
  if (deviceInfo.isSmallScreen) {
    document.body.classList.add('small-screen');
  }

  // تحسينات للأجهزة اللوحية
  if (deviceInfo.isTablet) {
    document.body.classList.add('tablet-mode');
  }
}

// الحصول على معلومات الجهاز
function getDeviceInfo() {
  var info = {
    isLowEnd: false,
    isSmallScreen: false,
    isTablet: false,
    isMobile: false,
    isDesktop: false
  };

  // فحص حجم الشاشة
  var screenWidth = window.screen ? window.screen.width : window.innerWidth;
  var screenHeight = window.screen ? window.screen.height : window.innerHeight;

  info.isSmallScreen = screenWidth < 768;
  info.isTablet = screenWidth >= 768 && screenWidth < 1024;
  info.isMobile = screenWidth < 768;
  info.isDesktop = screenWidth >= 1024;

  // فحص الأداء (تقدير تقريبي)
  var ua = navigator.userAgent.toLowerCase();
  var memory = navigator.deviceMemory || 4; // افتراضي 4GB
  var cores = navigator.hardwareConcurrency || 2; // افتراضي 2 cores

  // تحديد الأجهزة الضعيفة
  info.isLowEnd = memory < 2 || cores < 2 ||
                  ua.indexOf('android 4') > -1 ||
                  ua.indexOf('iphone os 7') > -1 ||
                  ua.indexOf('iphone os 8') > -1;

  return info;
}

// تحسين استخدام الذاكرة
function optimizeMemoryUsage() {
  // تنظيف البيانات المؤقتة كل 5 دقائق
  setInterval(function() {
    // مسح المتغيرات غير المستخدمة
    if (window.gc && typeof window.gc === 'function') {
      window.gc();
    }

    // تنظيف DOM من العناصر المخفية
    var hiddenElements = document.querySelectorAll('[style*="display: none"]');
    for (var i = 0; i < hiddenElements.length; i++) {
      var element = hiddenElements[i];
      if (element.dataset.temporary === 'true') {
        element.parentNode.removeChild(element);
      }
    }
  }, 300000); // 5 دقائق
}

// دعم اللمس للأجهزة المحمولة
function addTouchSupport() {
  if ('ontouchstart' in window || navigator.maxTouchPoints > 0) {
    document.body.classList.add('touch-device');

    // إضافة دعم السحب للتنقل
    var startX, startY;

    document.addEventListener('touchstart', function(e) {
      startX = e.touches[0].clientX;
      startY = e.touches[0].clientY;
    }, { passive: true });

    document.addEventListener('touchend', function(e) {
      if (!startX || !startY) return;

      var endX = e.changedTouches[0].clientX;
      var endY = e.changedTouches[0].clientY;

      var diffX = startX - endX;
      var diffY = startY - endY;

      // السحب الأفقي للتنقل
      if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {
        if (diffX > 0) {
          // سحب لليسار - الصفحة التالية
          nextRecord();
        } else {
          // سحب لليمين - الصفحة السابقة
          prevRecord();
        }
      }

      startX = null;
      startY = null;
    }, { passive: true });
  }
}

// دعم اختصارات لوحة المفاتيح المحسنة
function enhanceKeyboardSupport() {
  document.addEventListener('keydown', function(event) {
    // منع التداخل مع حقول الإدخال
    if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
      return;
    }

    switch(event.key) {
      case 'Home':
        event.preventDefault();
        showPage('welcomePage');
        break;
      case 'Insert':
        event.preventDefault();
        showPage('dataEntryPage');
        break;
      case 'F1':
        event.preventDefault();
        showUsageTips();
        break;
      case 'F5':
        event.preventDefault();
        location.reload();
        break;
      case 'F11':
        event.preventDefault();
        toggleFullscreen();
        break;
    }
  });
}

// تبديل وضع ملء الشاشة
function toggleFullscreen() {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen().catch(function(err) {
      console.log('لا يمكن تفعيل وضع ملء الشاشة:', err);
    });
  } else {
    document.exitFullscreen();
  }
}

// تحسين الطباعة لجميع المتصفحات
function enhancePrintSupport() {
  // إضافة أنماط خاصة بالطباعة
  var printStyles = document.createElement('style');
  printStyles.textContent = `
    @media print {
      body * { visibility: hidden; }
      .print-area, .print-area * { visibility: visible; }
      .print-area { position: absolute; left: 0; top: 0; width: 100%; }
      .no-print { display: none !important; }
    }
  `;
  document.head.appendChild(printStyles);
}

// تشغيل التحسينات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
  optimizeForDevice();
  optimizeMemoryUsage();
  addTouchSupport();
  enhanceKeyboardSupport();
  enhancePrintSupport();
});

// دعم تغيير اتجاه الشاشة
window.addEventListener('orientationchange', function() {
  setTimeout(function() {
    // إعادة حساب التخطيط
    window.dispatchEvent(new Event('resize'));

    // تحديث الإحصائيات
    updateStatistics();
  }, 100);
});

// دعم تغيير حجم النافذة
window.addEventListener('resize', function() {
  // تحديث معلومات الجهاز
  var deviceInfo = getDeviceInfo();

  // تطبيق التحسينات الجديدة
  if (deviceInfo.isSmallScreen) {
    document.body.classList.add('small-screen');
  } else {
    document.body.classList.remove('small-screen');
  }
});

  const lastUpdateElement = document.getElementById('lastUpdate');
  if (lastUpdateElement) {
    const lastUpdate = archiveData.length > 0 
      ? new Date(Math.max(...archiveData.map(r => r.createdAt ? new Date(r.createdAt).getTime() : 0)))
      : new Date();
    lastUpdateElement.textContent = lastUpdate.toLocaleDateString('ar-SA');
  }
}

// تنسيق حجم البيانات
function formatSize(bytes) {
  if (bytes < 1024) return bytes + ' بايت';
  if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(2) + ' كيلوبايت';
  return (bytes / (1024 * 1024)).toFixed(2) + ' ميجابايت';
}

// إحصائيات
function showStatistics() {
  const totalRecords = archiveData.length;
  const recordsWithNotes = archiveData.filter(r => r.notes).length;
  const departments = [...new Set(archiveData.map(r => r.department))];
  const currentYear = new Date().getFullYear();
  const thisYearRecords = archiveData.filter(r => r.year === currentYear).length;

  const statsHTML = `
    <div class="statistics">
      <h3>إحصائيات الأرشيف</h3>
      <div class="stats-grid">
        <div class="stat-item">
          <h4>${totalRecords}</h4>
          <p>إجمالي السجلات</p>
        </div>
        <div class="stat-item">
          <h4>${recordsWithNotes}</h4>
          <p>سجلات بهوامش</p>
        </div>
        <div class="stat-item">
          <h4>${departments.length}</h4>
          <p>عدد الدوائر</p>
        </div>
        <div class="stat-item">
          <h4>${thisYearRecords}</h4>
          <p>سجلات هذا العام</p>
        </div>
      </div>
    </div>
  `;

  document.getElementById('recordDisplay').innerHTML = statsHTML;
}
