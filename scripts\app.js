// قاعدة البيانات المحلية
let archiveData = JSON.parse(localStorage.getItem('archiveData')) || [];
let currentTheme = localStorage.getItem('currentTheme') || 'default';

// تطبيق الثيم المحفوظ عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', async function() {
  // تحميل البيانات من نظام الملفات إذا كان متاحاً
  if (window.electronAPI) {
    try {
      const data = await window.electronAPI.loadData();
      if (Array.isArray(data)) {
        archiveData = data;
        console.log('تم تحميل البيانات من نظام الملفات');
      }
    } catch (error) {
      console.error('خطأ في تحميل البيانات من نظام الملفات:', error);
      // استخدام البيانات المخزنة في localStorage كبديل
      const stored = localStorage.getItem('archiveData');
      if (stored) {
        try {
          archiveData = JSON.parse(stored);
        } catch (e) {
          console.error('خطأ في تحليل البيانات المخزنة:', e);
        }
      }
    }
  }
  
  // تطبيق الثيم وتحميل الصفحة الرئيسية
  applyTheme(currentTheme);
  loadWelcomePage();
  
  // تحديث الإحصائيات
  updateStatistics();
});

// وظائف التنقل
function showPage(pageId) {
  // إخفاء جميع الصفحات
  const pages = document.querySelectorAll('.page');
  pages.forEach(page => {
    page.style.display = 'none';
    page.classList.remove('fade-in');
  });

  // إظهار الصفحة المحددة مع تأثير الظهور
  const targetPage = document.getElementById(pageId);
  if (targetPage) {
    targetPage.style.display = 'block';
    setTimeout(() => {
      targetPage.classList.add('fade-in');
    }, 50);
  }

  // تحديث البيانات حسب الصفحة
  if (pageId === 'reportPage') {
    displayAllRecords();
  } else if (pageId === 'archivePage') {
    displayArchiveRecords();
  }
}

function loadWelcomePage() {
  showPage('welcomePage');
}

// وظائف إدخال البيانات
function addRecord() {
  const bookNumber = document.getElementById('bookNumber').value;
  const day = document.getElementById('day').value;
  const month = document.getElementById('month').value;
  const year = document.getElementById('year').value;
  const subject = document.getElementById('subject').value;
  const content = document.getElementById('content').value;
  const department = document.getElementById('department').value;
  const notes = document.getElementById('notes').value;

  // التحقق من البيانات المطلوبة
  if (!bookNumber || !day || !month || !year || !subject) {
    showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
    return;
  }

  // إنشاء سجل جديد
  const record = {
    id: Date.now(),
    bookNumber: bookNumber,
    date: `${day}/${month}/${year}`,
    day: parseInt(day),
    month: parseInt(month),
    year: parseInt(year),
    subject: subject,
    content: content,
    department: department,
    notes: notes,
    createdAt: new Date().toISOString()
  };

  // إضافة السجل إلى قاعدة البيانات
  archiveData.push(record);
  saveData();

  // إعادة تعيين النموذج
  document.getElementById('dataForm').reset();

  showNotification('تم حفظ السجل بنجاح', 'success');
  playSound('success');
}

// وظائف عرض البيانات
function displayAllRecords() {
  const tbody = document.getElementById('reportTableBody');
  tbody.innerHTML = '';

  archiveData.forEach((record, index) => {
    const row = document.createElement('tr');
    row.innerHTML = `
      <td>${index + 1}</td>
      <td>${record.bookNumber}</td>
      <td>${record.date}</td>
      <td>${record.subject}</td>
      <td>${record.content}</td>
      <td>${record.department}</td>
      <td>${record.notes || 'لا يوجد'}</td>
    `;
    tbody.appendChild(row);
  });
}

// متغيرات الأرشيف
let currentRecordIndex = 0;
let filteredData = [];

function displayArchiveRecords(data = null) {
  filteredData = data || archiveData;
  if (filteredData.length === 0) {
    document.getElementById('recordDisplay').innerHTML = '<p>لا توجد سجلات للعرض</p>';
    return;
  }

  currentRecordIndex = 0;
  displayCurrentRecord();
  updateNavigationButtons();
}

function displayCurrentRecord() {
  if (filteredData.length === 0) return;

  const record = filteredData[currentRecordIndex];
  document.getElementById('recordDisplay').innerHTML = `
    <div class="record-card">
      <h3>سجل رقم: ${record.bookNumber}</h3>
      <div class="record-details">
        <p><strong>التاريخ:</strong> ${record.date}</p>
        <p><strong>الموضوع:</strong> ${record.subject}</p>
        <p><strong>المحتوى:</strong> ${record.content}</p>
        <p><strong>الدائرة:</strong> ${record.department}</p>
        <p><strong>الهامش:</strong> ${record.notes || 'لا يوجد'}</p>
      </div>
      <div class="record-actions">
        <button onclick="editRecord(${record.id})" class="btn btn-warning">تعديل</button>
        <button onclick="deleteRecord(${record.id})" class="btn btn-danger">حذف</button>
      </div>
    </div>
  `;
}

function updateNavigationButtons() {
  document.getElementById('firstBtn').disabled = currentRecordIndex === 0;
  document.getElementById('prevBtn').disabled = currentRecordIndex === 0;
  document.getElementById('nextBtn').disabled = currentRecordIndex === filteredData.length - 1;
  document.getElementById('lastBtn').disabled = currentRecordIndex === filteredData.length - 1;

  document.getElementById('recordCounter').textContent = 
    `${currentRecordIndex + 1} من ${filteredData.length}`;
}

// وظائف التنقل في الأرشيف
function firstRecord() {
  currentRecordIndex = 0;
  displayCurrentRecord();
  updateNavigationButtons();
}

function prevRecord() {
  if (currentRecordIndex > 0) {
    currentRecordIndex--;
    displayCurrentRecord();
    updateNavigationButtons();
  }
}

function nextRecord() {
  if (currentRecordIndex < filteredData.length - 1) {
    currentRecordIndex++;
    displayCurrentRecord();
    updateNavigationButtons();
  }
}

function lastRecord() {
  currentRecordIndex = filteredData.length - 1;
  displayCurrentRecord();
  updateNavigationButtons();
}

// تعديل وحذف السجلات
function editRecord(id) {
  const record = archiveData.find(r => r.id === id);
  if (!record) return;

  // ملء النموذج بالبيانات الحالية
  document.getElementById('bookNumber').value = record.bookNumber;
  document.getElementById('day').value = record.day;
  document.getElementById('month').value = record.month;
  document.getElementById('year').value = record.year;
  document.getElementById('subject').value = record.subject;
  document.getElementById('content').value = record.content;
  document.getElementById('department').value = record.department;
  document.getElementById('notes').value = record.notes;

  // حذف السجل القديم
  archiveData = archiveData.filter(r => r.id !== id);
  saveData();

  // الانتقال إلى صفحة إدخال البيانات
  showPage('dataEntryPage');
  showNotification('يمكنك الآن تعديل البيانات والحفظ', 'info');
}

function deleteRecord(id) {
  if (confirm('هل أنت متأكد من حذف هذا السجل؟')) {
    archiveData = archiveData.filter(r => r.id !== id);
    saveData();
    displayArchiveRecords();
    showNotification('تم حذف السجل بنجاح', 'success');
    playSound('delete');
  }
}

// وظائف التصفية
function applyFilter() {
  const fromDay = document.getElementById('fromDay').value;
  const fromMonth = document.getElementById('fromMonth').value;
  const fromYear = document.getElementById('fromYear').value;
  const toDay = document.getElementById('toDay').value;
  const toMonth = document.getElementById('toMonth').value;
  const toYear = document.getElementById('toYear').value;
  const subjectFilter = document.getElementById('subjectFilter').value.toLowerCase();
  const employeeFilter = document.getElementById('employeeFilter').value.toLowerCase();
  const departmentFilter = document.getElementById('departmentFilter').value.toLowerCase();
  const notesFilter = document.getElementById('notesFilter').value;

  let filtered = archiveData.filter(record => {
    // تصفية التاريخ
    if (fromDay && fromMonth && fromYear) {
      const fromDate = new Date(fromYear, fromMonth - 1, fromDay);
      const recordDate = new Date(record.year, record.month - 1, record.day);
      if (recordDate < fromDate) return false;
    }

    if (toDay && toMonth && toYear) {
      const toDate = new Date(toYear, toMonth - 1, toDay);
      const recordDate = new Date(record.year, record.month - 1, record.day);
      if (recordDate > toDate) return false;
    }

    // تصفية الموضوع
    if (subjectFilter && !record.subject.toLowerCase().includes(subjectFilter)) {
      return false;
    }

    // تصفية الموظف
    if (employeeFilter && !record.content.toLowerCase().includes(employeeFilter)) {
      return false;
    }

    // تصفية الدائرة
    if (departmentFilter && !record.department.toLowerCase().includes(departmentFilter)) {
      return false;
    }

    // تصفية الهامش
    if (notesFilter === 'has-notes' && !record.notes) {
      return false;
    }
    if (notesFilter === 'no-notes' && record.notes) {
      return false;
    }

    return true;
  });

  displayArchiveRecords(filtered);
  showPage('archivePage');
  showNotification(`تم العثور على ${filtered.length} سجل`, 'info');
}

function clearFilter() {
  document.getElementById('filterForm').reset();
  displayArchiveRecords();
  showNotification('تم إلغاء التصفية', 'info');
}

// وظائف الثيمات
const themes = {
  default: {
    primary: '#3498db',
    secondary: '#2ecc71',
    accent: '#e74c3c',
    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    text: '#2c3e50'
  },
  ocean: {
    primary: '#1abc9c',
    secondary: '#3498db',
    accent: '#e67e22',
    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    text: '#2c3e50'
  },
  sunset: {
    primary: '#e74c3c',
    secondary: '#f39c12',
    accent: '#9b59b6',
    background: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
    text: '#2c3e50'
  },
  forest: {
    primary: '#27ae60',
    secondary: '#2ecc71',
    accent: '#f39c12',
    background: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
    text: '#2c3e50'
  }
};

function applyTheme(themeName) {
  const theme = themes[themeName];
  if (!theme) return;

  const root = document.documentElement;
  root.style.setProperty('--primary-color', theme.primary);
  root.style.setProperty('--secondary-color', theme.secondary);
  root.style.setProperty('--accent-color', theme.accent);
  root.style.setProperty('--background-gradient', theme.background);
  root.style.setProperty('--text-color', theme.text);

  currentTheme = themeName;
  localStorage.setItem('currentTheme', currentTheme);

  showNotification('تم تطبيق الثيم بنجاح', 'success');
}

// وظائف مساعدة
async function saveData() {
  try {
    // حفظ البيانات في localStorage كنسخة احتياطية
    localStorage.setItem('archiveData', JSON.stringify(archiveData));
    
    // حفظ البيانات باستخدام واجهة Electron إذا كانت متوفرة
    if (window.electronAPI) {
      await window.electronAPI.saveData(archiveData);
    }
    
    return true;
  } catch (error) {
    console.error('خطأ في حفظ البيانات:', error);
    showNotification('خطأ في حفظ البيانات', 'error');
    return false;
  }
}

function showNotification(message, type = 'info') {
  const notification = document.createElement('div');
  notification.className = `notification notification-${type}`;
  notification.textContent = message;

  document.body.appendChild(notification);

  setTimeout(() => {
    notification.classList.add('show');
  }, 100);

  setTimeout(() => {
    notification.classList.remove('show');
    setTimeout(() => {
      document.body.removeChild(notification);
    }, 300);
  }, 3000);
}

function playSound(type) {
  // يمكن إضافة الأصوات هنا
  try {
    const audio = new Audio(`sounds/${type}.mp3`);
    audio.volume = 0.3;
    audio.play().catch(() => {
      // تجاهل الأخطاء إذا لم تكن الأصوات متوفرة
    });
  } catch (error) {
    // تجاهل أخطاء الصوت
  }
}

// تصدير البيانات
async function exportData() {
  try {
    // استخدام واجهة Electron للتصدير إذا كانت متوفرة
    if (window.electronAPI) {
      await window.electronAPI.exportData(archiveData);
      showNotification('تم تصدير البيانات بنجاح', 'success');
    } else {
      // استخدام طريقة التصدير التقليدية للمتصفح
      const dataStr = JSON.stringify(archiveData, null, 2);
      const blob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(blob);

      const a = document.createElement('a');
      a.href = url;
      a.download = `archive-backup-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);

      URL.revokeObjectURL(url);
      showNotification('تم تصدير البيانات بنجاح', 'success');
    }
  } catch (error) {
    console.error('خطأ في تصدير البيانات:', error);
    showNotification('خطأ في تصدير البيانات: ' + error, 'error');
  }
}

// استيراد البيانات
async function importData(event) {
  try {
    // استخدام واجهة Electron للاستيراد إذا كانت متوفرة
    if (window.electronAPI) {
      const importedData = await window.electronAPI.importData();
      if (Array.isArray(importedData)) {
        archiveData = importedData;
        saveData();
        showNotification('تم استيراد البيانات بنجاح', 'success');
      } else {
        showNotification('ملف البيانات غير صحيح', 'error');
      }
    } else {
      // استخدام طريقة الاستيراد التقليدية للمتصفح
      const file = event.target.files[0];
      if (!file) return;

      const reader = new FileReader();
      reader.onload = function(e) {
        try {
          const importedData = JSON.parse(e.target.result);
          if (Array.isArray(importedData)) {
            archiveData = importedData;
            saveData();
            showNotification('تم استيراد البيانات بنجاح', 'success');
          } else {
            showNotification('ملف البيانات غير صحيح', 'error');
          }
        } catch (error) {
          showNotification('خطأ في قراءة الملف', 'error');
        }
      };
      reader.readAsText(file);
    }
  } catch (error) {
    console.error('خطأ في استيراد البيانات:', error);
    showNotification('خطأ في استيراد البيانات: ' + error, 'error');
  }
}

// البحث السريع
function quickSearch() {
  const searchTerm = document.getElementById('quickSearchInput').value.toLowerCase();
  if (!searchTerm) {
    displayArchiveRecords();
    return;
  }

  const filtered = archiveData.filter(record => 
    record.bookNumber.toLowerCase().includes(searchTerm) ||
    record.subject.toLowerCase().includes(searchTerm) ||
    record.content.toLowerCase().includes(searchTerm) ||
    record.department.toLowerCase().includes(searchTerm) ||
    (record.notes && record.notes.toLowerCase().includes(searchTerm))
  );

  displayArchiveRecords(filtered);
  showNotification(`تم العثور على ${filtered.length} نتيجة`, 'info');
}

// تحديث الإحصائيات في الصفحة الرئيسية
function updateStatistics() {
  const totalRecords = archiveData.length;
  const today = new Date();
  const todayRecords = archiveData.filter(record => {
    const recordDate = new Date(record.year, record.month - 1, record.day);
    return recordDate.toDateString() === today.toDateString();
  }).length;
  const departments = [...new Set(archiveData.map(r => r.department))].filter(Boolean).length;

  document.getElementById('totalRecordsCount').textContent = totalRecords;
  document.getElementById('todayRecordsCount').textContent = todayRecords;
  document.getElementById('departmentsCount').textContent = departments;

  // تحديث معلومات البرنامج
  const dataSize = JSON.stringify(archiveData).length;
  const dbSizeElement = document.getElementById('dbSize');
  if (dbSizeElement) {
    dbSizeElement.textContent = formatSize(dataSize);
  }

  const lastUpdateElement = document.getElementById('lastUpdate');
  if (lastUpdateElement) {
    const lastUpdate = archiveData.length > 0 
      ? new Date(Math.max(...archiveData.map(r => r.createdAt ? new Date(r.createdAt).getTime() : 0)))
      : new Date();
    lastUpdateElement.textContent = lastUpdate.toLocaleDateString('ar-SA');
  }
}

// تنسيق حجم البيانات
function formatSize(bytes) {
  if (bytes < 1024) return bytes + ' بايت';
  if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(2) + ' كيلوبايت';
  return (bytes / (1024 * 1024)).toFixed(2) + ' ميجابايت';
}

// إحصائيات
function showStatistics() {
  const totalRecords = archiveData.length;
  const recordsWithNotes = archiveData.filter(r => r.notes).length;
  const departments = [...new Set(archiveData.map(r => r.department))];
  const currentYear = new Date().getFullYear();
  const thisYearRecords = archiveData.filter(r => r.year === currentYear).length;

  const statsHTML = `
    <div class="statistics">
      <h3>إحصائيات الأرشيف</h3>
      <div class="stats-grid">
        <div class="stat-item">
          <h4>${totalRecords}</h4>
          <p>إجمالي السجلات</p>
        </div>
        <div class="stat-item">
          <h4>${recordsWithNotes}</h4>
          <p>سجلات بهوامش</p>
        </div>
        <div class="stat-item">
          <h4>${departments.length}</h4>
          <p>عدد الدوائر</p>
        </div>
        <div class="stat-item">
          <h4>${thisYearRecords}</h4>
          <p>سجلات هذا العام</p>
        </div>
      </div>
    </div>
  `;

  document.getElementById('recordDisplay').innerHTML = statsHTML;
}
