// المتغيرات العامة
let currentRecordIndex = 0;
let filteredRecords = [];
let allRecords = [];

// تهيئة التطبيق
document.addEventListener('DOMContentLoaded', function() {
  initializeApp();
  loadRecords();
  setupEventListeners();
  applyTheme();
});

// تهيئة التطبيق
function initializeApp() {
  // تحميل البيانات من التخزين المحلي
  allRecords = loadFromStorage('archiveRecords') || [];
  filteredRecords = [...allRecords];
  
  // تحديث عداد السجلات
  updateRecordCounter();
  
  // عرض أول سجل إذا كان متوفراً
  if (allRecords.length > 0) {
    displayRecord(0);
  }
  
  // تحديث التقارير
  updateReportsTable();
  
  // تحديث قائمة الدوائر في التصفية
  updateDepartmentFilter();
}

// إعداد مستمعات الأحداث
function setupEventListeners() {
  // نموذج إدخال البيانات
  const dataForm = document.getElementById('data-form');
  if (dataForm) {
    dataForm.addEventListener('submit', handleFormSubmit);
  }
  
  // البحث في الأرشيف
  const searchInput = document.getElementById('search-input');
  if (searchInput) {
    searchInput.addEventListener('input', handleSearch);
  }
  
  // نموذج التصفية
  const filterForm = document.getElementById('filter-form');
  if (filterForm) {
    filterForm.addEventListener('submit', handleFilter);
    filterForm.addEventListener('reset', resetFilter);
  }
  
  // اختيار السمة
  const themeOptions = document.querySelectorAll('.theme-option');
  themeOptions.forEach(option => {
    option.addEventListener('click', function() {
      const theme = this.dataset.theme;
      changeTheme(theme);
    });
  });
  
  // إغلاق النافذة المنبثقة
  const modal = document.getElementById('modal');
  const closeBtn = document.querySelector('.close');
  const cancelBtn = document.getElementById('modal-cancel');
  
  if (closeBtn) {
    closeBtn.addEventListener('click', closeModal);
  }
  
  if (cancelBtn) {
    cancelBtn.addEventListener('click', closeModal);
  }
  
  if (modal) {
    modal.addEventListener('click', function(e) {
      if (e.target === modal) {
        closeModal();
      }
    });
  }
}

// عرض الشاشة المحددة
function showScreen(screenId) {
  // إخفاء جميع الشاشات
  const screens = document.querySelectorAll('.screen');
  screens.forEach(screen => {
    screen.classList.remove('active');
  });
  
  // عرض الشاشة المحددة
  const targetScreen = document.getElementById(screenId);
  if (targetScreen) {
    targetScreen.classList.add('active');
    
    // تحديث المحتوى حسب الشاشة
    if (screenId === 'reports') {
      updateReportsTable();
    } else if (screenId === 'archive') {
      displayCurrentRecord();
    }
  }
}

// معالجة إرسال النموذج
function handleFormSubmit(e) {
  e.preventDefault();
  
  const formData = new FormData(e.target);
  const record = {
    id: generateId(),
    bookNumber: formData.get('bookNumber'),
    date: {
      day: parseInt(formData.get('day')),
      month: parseInt(formData.get('month')),
      year: parseInt(formData.get('year'))
    },
    subject: formData.get('subject'),
    content: formData.get('content'),
    department: formData.get('department'),
    notes: formData.get('notes') || '',
    createdAt: new Date().toISOString()
  };
  
  // التحقق من صحة البيانات
  if (!validateRecord(record)) {
    showNotification('يرجى التأكد من صحة البيانات المدخلة', 'error');
    return;
  }
  
  // حفظ السجل
  allRecords.push(record);
  saveToStorage('archiveRecords', allRecords);
  
  // تحديث القوائم
  filteredRecords = [...allRecords];
  updateRecordCounter();
  updateReportsTable();
  updateDepartmentFilter();
  
  // إعادة تعيين النموذج
  e.target.reset();
  
  // عرض رسالة نجاح
  showNotification('تم حفظ السجل بنجاح', 'success');
  
  // الانتقال إلى آخر سجل
  currentRecordIndex = allRecords.length - 1;
  displayRecord(currentRecordIndex);
}

// التحقق من صحة السجل
function validateRecord(record) {
  if (!record.bookNumber.trim()) return false;
  if (!record.subject.trim()) return false;
  if (!record.content.trim()) return false;
  if (!record.department.trim()) return false;
  
  const { day, month, year } = record.date;
  if (!day || !month || !year) return false;
  if (day < 1 || day > 31) return false;
  if (month < 1 || month > 12) return false;
  if (year < 1900 || year > 2100) return false;
  
  return true;
}

// توليد معرف فريد
function generateId() {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

// تحميل السجلات
function loadRecords() {
  allRecords = loadFromStorage('archiveRecords') || [];
  filteredRecords = [...allRecords];
}

// عرض السجل الحالي
function displayCurrentRecord() {
  if (filteredRecords.length > 0) {
    displayRecord(currentRecordIndex);
  } else {
    displayEmptyRecord();
  }
}

// عرض سجل محدد
function displayRecord(index) {
  const record = filteredRecords[index];
  const recordContent = document.getElementById('record-content');
  
  if (!record) {
    displayEmptyRecord();
    return;
  }
  
  const dateStr = `${record.date.day}/${record.date.month}/${record.date.year}`;
  
  recordContent.innerHTML = `
    <div class="record-details">
      <div class="record-header">
        <h3>رقم الكتاب: ${record.bookNumber}</h3>
        <span class="record-date">${dateStr}</span>
      </div>
      <div class="record-field">
        <label>الموضوع:</label>
        <p>${record.subject}</p>
      </div>
      <div class="record-field">
        <label>الفحوى أو اسم الموظف المعني:</label>
        <p>${record.content}</p>
      </div>
      <div class="record-field">
        <label>الدائرة المعنية:</label>
        <p>${record.department}</p>
      </div>
      ${record.notes ? `
        <div class="record-field">
          <label>الهامش:</label>
          <p>${record.notes}</p>
        </div>
      ` : ''}
      <div class="record-meta">
        <small>تاريخ الإنشاء: ${new Date(record.createdAt).toLocaleDateString('ar-SA')}</small>
      </div>
    </div>
  `;
}

// عرض رسالة فارغة
function displayEmptyRecord() {
  const recordContent = document.getElementById('record-content');
  recordContent.innerHTML = `
    <div class="empty-state">
      <i class="fas fa-inbox" style="font-size: 4rem; color: #bdc3c7; margin-bottom: 20px;"></i>
      <h3>لا توجد سجلات</h3>
      <p>لم يتم العثور على أي سجلات في الأرشيف</p>
    </div>
  `;
}

// التنقل بين السجلات
function navigateToFirst() {
  if (filteredRecords.length > 0) {
    currentRecordIndex = 0;
    displayRecord(currentRecordIndex);
    updateRecordCounter();
  }
}

function navigatePrevious() {
  if (currentRecordIndex > 0) {
    currentRecordIndex--;
    displayRecord(currentRecordIndex);
    updateRecordCounter();
  }
}

function navigateNext() {
  if (currentRecordIndex < filteredRecords.length - 1) {
    currentRecordIndex++;
    displayRecord(currentRecordIndex);
    updateRecordCounter();
  }
}

function navigateToLast() {
  if (filteredRecords.length > 0) {
    currentRecordIndex = filteredRecords.length - 1;
    displayRecord(currentRecordIndex);
    updateRecordCounter();
  }
}

// تحديث عداد السجلات
function updateRecordCounter() {
  const currentRecordSpan = document.getElementById('current-record');
  const totalRecordsSpan = document.getElementById('total-records');
  
  if (currentRecordSpan) {
    currentRecordSpan.textContent = filteredRecords.length > 0 ? currentRecordIndex + 1 : 0;
  }
  
  if (totalRecordsSpan) {
    totalRecordsSpan.textContent = filteredRecords.length;
  }
}

// البحث في السجلات
function handleSearch(e) {
  const searchTerm = e.target.value.toLowerCase().trim();
  
  if (!searchTerm) {
    filteredRecords = [...allRecords];
  } else {
    filteredRecords = allRecords.filter(record => {
      return record.bookNumber.toLowerCase().includes(searchTerm) ||
             record.subject.toLowerCase().includes(searchTerm) ||
             record.content.toLowerCase().includes(searchTerm) ||
             record.department.toLowerCase().includes(searchTerm) ||
             (record.notes && record.notes.toLowerCase().includes(searchTerm));
    });
  }
  
  // إعادة تعيين الفهرس
  currentRecordIndex = 0;
  displayCurrentRecord();
  updateRecordCounter();
}

// تحديث جدول التقارير
function updateReportsTable() {
  const tbody = document.getElementById('reports-tbody');
  if (!tbody) return;
  
  tbody.innerHTML = '';
  
  allRecords.forEach((record, index) => {
    const row = document.createElement('tr');
    const dateStr = `${record.date.day}/${record.date.month}/${record.date.year}`;
    
    row.innerHTML = `
      <td>${record.bookNumber}</td>
      <td>${dateStr}</td>
      <td>${record.subject}</td>
      <td>${record.content.substring(0, 50)}${record.content.length > 50 ? '...' : ''}</td>
      <td>${record.department}</td>
      <td>${record.notes ? record.notes.substring(0, 30) + '...' : 'لا يوجد'}</td>
      <td>
        <button class="edit-btn small" onclick="editRecordFromTable(${index})">
          <i class="fas fa-edit"></i>
        </button>
        <button class="delete-btn small" onclick="deleteRecordFromTable(${index})">
          <i class="fas fa-trash"></i>
        </button>
      </td>
    `;
    
    tbody.appendChild(row);
  });
}

// تعديل السجل
function editRecord() {
  const currentRecord = filteredRecords[currentRecordIndex];
  if (!currentRecord) return;
  
  showEditModal(currentRecord);
}

function editRecordFromTable(index) {
  const record = allRecords[index];
  showEditModal(record);
}

// حذف السجل
function deleteRecord() {
  const currentRecord = filteredRecords[currentRecordIndex];
  if (!currentRecord) return;
  
  showConfirm(
    'تأكيد الحذف',
    'هل أنت متأكد من حذف هذا السجل؟ لا يمكن التراجع عن هذا الإجراء.',
    () => {
      const originalIndex = allRecords.findIndex(r => r.id === currentRecord.id);
      if (originalIndex !== -1) {
        allRecords.splice(originalIndex, 1);
        saveToStorage('archiveRecords', allRecords);
        
        filteredRecords = [...allRecords];
        
        if (currentRecordIndex >= filteredRecords.length) {
          currentRecordIndex = Math.max(0, filteredRecords.length - 1);
        }
        
        displayCurrentRecord();
        updateRecordCounter();
        updateReportsTable();
        updateDepartmentFilter();
        
        showNotification('تم حذف السجل بنجاح', 'success');
      }
    }
  );
}

function deleteRecordFromTable(index) {
  const record = allRecords[index];
  
  showConfirm(
    'تأكيد الحذف',
    'هل أنت متأكد من حذف هذا السجل؟ لا يمكن التراجع عن هذا الإجراء.',
    () => {
      allRecords.splice(index, 1);
      saveToStorage('archiveRecords', allRecords);
      
      filteredRecords = [...allRecords];
      updateReportsTable();
      updateRecordCounter();
      updateDepartmentFilter();
      
      showNotification('تم حذف السجل بنجاح', 'success');
    }
  );
}

// تحديث قائمة الدوائر في التصفية
function updateDepartmentFilter() {
  const select = document.getElementById('filter-department');
  if (!select) return;
  
  const departments = [...new Set(allRecords.map(record => record.department))];
  
  // الاحتفاظ بالخيار الأول
  select.innerHTML = '<option value="">جميع الدوائر</option>';
  
  departments.forEach(dept => {
    const option = document.createElement('option');
    option.value = dept;
    option.textContent = dept;
    select.appendChild(option);
  });
}

// عرض النافذة المنبثقة للتأكيد
function showConfirm(title, message, onConfirm) {
  const modal = document.getElementById('modal');
  const modalTitle = document.getElementById('modal-title');
  const modalMessage = document.getElementById('modal-message');
  const confirmBtn = document.getElementById('modal-confirm');
  
  modalTitle.textContent = title;
  modalMessage.textContent = message;
  
  confirmBtn.onclick = () => {
    onConfirm();
    closeModal();
  };
  
  modal.style.display = 'block';
}

// إغلاق النافذة المنبثقة
function closeModal() {
  const modal = document.getElementById('modal');
  modal.style.display = 'none';
}

// عرض الإشعارات
function showNotification(message, type = 'info') {
  // إنشاء عنصر الإشعار
  const notification = document.createElement('div');
  notification.className = `notification ${type}`;
  notification.innerHTML = `
    <div class="notification-content">
      <i class="fas ${getNotificationIcon(type)}"></i>
      <span>${message}</span>
      <button class="notification-close">&times;</button>
    </div>
  `;
  
  // إضافة الإشعار إلى الصفحة
  document.body.appendChild(notification);
  
  // إضافة أنماط CSS للإشعار
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: ${getNotificationColor(type)};
    color: white;
    padding: 15px 20px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    z-index: 10000;
    max-width: 400px;
    animation: slideDown 0.3s ease-out;
  `;
  
  // إغلاق الإشعار تلقائياً
  setTimeout(() => {
    notification.classList.add('hide');
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 500);
  }, 3000);
  
  // إغلاق الإشعار عند النقر على زر الإغلاق
  const closeBtn = notification.querySelector('.notification-close');
  closeBtn.addEventListener('click', () => {
    notification.classList.add('hide');
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 500);
  });
}

// الحصول على أيقونة الإشعار
function getNotificationIcon(type) {
  switch (type) {
    case 'success': return 'fa-check-circle';
    case 'error': return 'fa-exclamation-circle';
    case 'warning': return 'fa-exclamation-triangle';
    default: return 'fa-info-circle';
  }
}

// الحصول على لون الإشعار
function getNotificationColor(type) {
  switch (type) {
    case 'success': return '#27ae60';
    case 'error': return '#e74c3c';
    case 'warning': return '#f39c12';
    default: return '#3498db';
  }
}

// تصدير البيانات
function exportData() {
  if (allRecords.length === 0) {
    showNotification('لا توجد بيانات للتصدير', 'warning');
    return;
  }
  
  const dataStr = JSON.stringify(allRecords, null, 2);
  const dataBlob = new Blob([dataStr], { type: 'application/json' });
  
  const link = document.createElement('a');
  link.href = URL.createObjectURL(dataBlob);
  link.download = `archive_backup_${new Date().toISOString().split('T')[0]}.json`;
  link.click();
  
  showNotification('تم تصدير البيانات بنجاح', 'success');
}

// استيراد البيانات
function importData() {
  const input = document.createElement('input');
  input.type = 'file';
  input.accept = '.json';
  
  input.onchange = function(e) {
    const file = e.target.files[0];
    if (!file) return;
    
    const reader = new FileReader();
    reader.onload = function(e) {
      try {
        const importedData = JSON.parse(e.target.result);
        
        if (Array.isArray(importedData)) {
          allRecords = importedData;
          saveToStorage('archiveRecords', allRecords);
          
          filteredRecords = [...allRecords];
          currentRecordIndex = 0;
          
          displayCurrentRecord();
          updateRecordCounter();
          updateReportsTable();
          updateDepartmentFilter();
          
          showNotification('تم استيراد البيانات بنجاح', 'success');
        } else {
          showNotification('ملف البيانات غير صحيح', 'error');
        }
      } catch (error) {
        showNotification('خطأ في قراءة الملف', 'error');
      }
    };
    
    reader.readAsText(file);
  };
  
  input.click();
}

// حذف جميع البيانات
function clearAllData() {
  showConfirm(
    'تأكيد حذف جميع البيانات',
    'هل أنت متأكد من حذف جميع البيانات؟ سيتم فقدان جميع السجلات نهائياً.',
    () => {
      allRecords = [];
      filteredRecords = [];
      currentRecordIndex = 0;
      
      saveToStorage('archiveRecords', allRecords);
      
      displayCurrentRecord();
      updateRecordCounter();
      updateReportsTable();
      updateDepartmentFilter();
      
      showNotification('تم حذف جميع البيانات', 'success');
    }
  );
}

// عرض نموذج التعديل
function showEditModal(record) {
  // هذه الوظيفة يمكن تطويرها لاحقاً لعرض نموذج تعديل منفصل
  // حالياً سنقوم بملء نموذج إدخال البيانات بالقيم الحالية
  
  showScreen('data-entry');
  
  // ملء النموذج بالبيانات الحالية
  document.getElementById('book-number').value = record.bookNumber;
  document.querySelector('input[name="day"]').value = record.date.day;
  document.querySelector('input[name="month"]').value = record.date.month;
  document.querySelector('input[name="year"]').value = record.date.year;
  document.getElementById('subject').value = record.subject;
  document.getElementById('content').value = record.content;
  document.getElementById('department').value = record.department;
  document.getElementById('notes').value = record.notes || '';
  
  showNotification('تم تحميل البيانات للتعديل', 'info');
}