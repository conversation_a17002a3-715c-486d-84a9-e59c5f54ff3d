// ملف Electron Preload لربط التطبيق بنظام الملفات
// المطور: المحاسب المبرمج علي عاجل خشان المحنة

const { contextBridge, ipcRenderer } = require('electron');

// تعريض واجهة برمجة التطبيقات للتطبيق
contextBridge.exposeInMainWorld('electronAPI', {
  // حفظ البيانات
  saveData: async (data) => {
    return await ipcRenderer.invoke('save-data', data);
  },

  // تحميل البيانات
  loadData: async () => {
    return await ipcRenderer.invoke('load-data');
  },

  // حفظ ملف في مسار محدد
  saveToFile: async (filePath, content) => {
    return await ipcRenderer.invoke('save-file', filePath, content);
  },

  // تحميل ملف من مسار محدد
  loadFromFile: async (filePath) => {
    return await ipcRenderer.invoke('load-file', filePath);
  },

  // اختيار ملف للاستيراد
  selectFile: async () => {
    return await ipcRenderer.invoke('select-file');
  },

  // اختيار مجلد للحفظ
  selectFolder: async () => {
    return await ipcRenderer.invoke('select-folder');
  },

  // إنشاء نسخة احتياطية
  createBackup: async (data) => {
    return await ipcRenderer.invoke('create-backup', data);
  },

  // الحصول على معلومات النظام
  getSystemInfo: async () => {
    return await ipcRenderer.invoke('get-system-info');
  },

  // إنشاء مجلد
  createFolder: async (folderPath) => {
    return await ipcRenderer.invoke('create-folder', folderPath);
  }
});

// إضافة معلومات التطبيق
contextBridge.exposeInMainWorld('appInfo', {
  name: 'برنامج الأرشفة الإلكترونية',
  version: '2.0.0',
  developer: 'المحاسب المبرمج علي عاجل خشان المحنة',
  platform: 'Electron',
  isElectron: true
});

// إضافة دوال مساعدة
contextBridge.exposeInMainWorld('utils', {
  // تنسيق التاريخ
  formatDate: (date) => {
    return new Date(date).toLocaleDateString('ar-SA');
  },

  // تنسيق حجم الملف
  formatFileSize: (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  },

  // إنشاء معرف فريد
  generateId: () => {
    return Date.now() + Math.random().toString(36).substr(2, 9);
  }
});

console.log('تم تحميل Electron Preload بنجاح - المطور: المحاسب المبرمج علي عاجل خشان المحنة');
