const { ipc<PERSON>ender<PERSON> } = require('electron');

// إضافة واجهة برمجة التطبيق للتواصل مع العمليات الرئيسية
window.electronAPI = {
  // حفظ البيانات في ملف محلي
  saveData: (data) => {
    return new Promise((resolve, reject) => {
      ipcRenderer.send('save-data', data);
      ipcRenderer.once('save-data-reply', (event, response) => {
        if (response.success) {
          resolve(response);
        } else {
          reject(response.error);
        }
      });
    });
  },

  // تحميل البيانات من ملف محلي
  loadData: () => {
    return new Promise((resolve, reject) => {
      ipcRenderer.send('load-data');
      ipcRenderer.once('load-data-reply', (event, response) => {
        if (response.success) {
          resolve(response.data);
        } else {
          reject(response.error);
        }
      });
    });
  },

  // تصدير البيانات إلى ملف
  exportData: (data) => {
    return new Promise((resolve, reject) => {
      ipcRenderer.send('export-data', data);
      ipcRenderer.once('export-data-reply', (event, response) => {
        if (response.success) {
          resolve(response);
        } else {
          reject(response.error);
        }
      });
    });
  },

  // استيراد البيانات من ملف
  importData: () => {
    return new Promise((resolve, reject) => {
      ipcRenderer.send('import-data');
      ipcRenderer.once('import-data-reply', (event, response) => {
        if (response.success) {
          resolve(response.data);
        } else {
          reject(response.error);
        }
      });
    });
  }
};