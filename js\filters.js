// إدارة التصفية والبحث

// معالجة تصفية البيانات
function handleFilter(e) {
  e.preventDefault();
  
  const formData = new FormData(e.target);
  const filters = {
    dateRange: {
      from: {
        day: formData.get('fromDay') ? parseInt(formData.get('fromDay')) : null,
        month: formData.get('fromMonth') ? parseInt(formData.get('fromMonth')) : null,
        year: formData.get('fromYear') ? parseInt(formData.get('fromYear')) : null
      },
      to: {
        day: formData.get('toDay') ? parseInt(formData.get('toDay')) : null,
        month: formData.get('toMonth') ? parseInt(formData.get('toMonth')) : null,
        year: formData.get('toYear') ? parseInt(formData.get('toYear')) : null
      }
    },
    subject: formData.get('filterSubject')?.trim() || '',
    employee: formData.get('filterEmployee')?.trim() || '',
    department: formData.get('filterDepartment') || '',
    notes: formData.get('filterNotes') || ''
  };
  
  const filteredResults = applyFilters(allRecords, filters);
  displayFilterResults(filteredResults);
}

// تطبيق المرشحات على البيانات
function applyFilters(records, filters) {
  return records.filter(record => {
    // تصفية حسب التاريخ
    if (!matchesDateRange(record.date, filters.dateRange)) {
      return false;
    }
    
    // تصفية حسب الموضوع
    if (filters.subject && !record.subject.toLowerCase().includes(filters.subject.toLowerCase())) {
      return false;
    }
    
    // تصفية حسب اسم الموظف
    if (filters.employee && !record.content.toLowerCase().includes(filters.employee.toLowerCase())) {
      return false;
    }
    
    // تصفية حسب الدائرة
    if (filters.department && record.department !== filters.department) {
      return false;
    }
    
    // تصفية حسب الهامش
    if (filters.notes === 'with-notes' && (!record.notes || record.notes.trim() === '')) {
      return false;
    }
    if (filters.notes === 'without-notes' && record.notes && record.notes.trim() !== '') {
      return false;
    }
    
    return true;
  });
}

// التحقق من تطابق نطاق التاريخ
function matchesDateRange(recordDate, dateRange) {
  const { from, to } = dateRange;
  
  // تحويل تاريخ السجل إلى كائن Date
  const recordDateObj = new Date(recordDate.year, recordDate.month - 1, recordDate.day);
  
  // التحقق من التاريخ البداية
  if (from.day && from.month && from.year) {
    const fromDate = new Date(from.year, from.month - 1, from.day);
    if (recordDateObj < fromDate) {
      return false;
    }
  }
  
  // التحقق من التاريخ النهاية
  if (to.day && to.month && to.year) {
    const toDate = new Date(to.year, to.month - 1, to.day);
    if (recordDateObj > toDate) {
      return false;
    }
  }
  
  return true;
}

// عرض نتائج التصفية
function displayFilterResults(results) {
  const tbody = document.getElementById('filter-tbody');
  if (!tbody) return;
  
  tbody.innerHTML = '';
  
  if (results.length === 0) {
    tbody.innerHTML = `
      <tr>
        <td colspan="6" style="text-align: center; padding: 30px;">
          <i class="fas fa-search" style="font-size: 2rem; color: #bdc3c7; margin-bottom: 10px;"></i>
          <p>لم يتم العثور على نتائج تطابق معايير البحث</p>
        </td>
      </tr>
    `;
    return;
  }
  
  results.forEach(record => {
    const row = document.createElement('tr');
    const dateStr = `${record.date.day}/${record.date.month}/${record.date.year}`;
    
    row.innerHTML = `
      <td>${record.bookNumber}</td>
      <td>${dateStr}</td>
      <td>${record.subject}</td>
      <td>${record.content.substring(0, 50)}${record.content.length > 50 ? '...' : ''}</td>
      <td>${record.department}</td>
      <td>${record.notes ? record.notes.substring(0, 30) + '...' : 'لا يوجد'}</td>
    `;
    
    tbody.appendChild(row);
  });
  
  // عرض إحصائيات النتائج
  showNotification(`تم العثور على ${results.length} سجل يطابق معايير البحث`, 'success');
}

// إعادة تعيين التصفية
function resetFilter() {
  const tbody = document.getElementById('filter-tbody');
  if (tbody) {
    tbody.innerHTML = '';
  }
  
  showNotification('تم إلغاء التصفية', 'info');
}

// البحث المتقدم
function advancedSearch(query, options = {}) {
  const {
    fields = ['bookNumber', 'subject', 'content', 'department', 'notes'],
    matchType = 'partial', // 'exact', 'partial', 'fuzzy'
    caseSensitive = false
  } = options;
  
  const searchTerm = caseSensitive ? query : query.toLowerCase();
  
  return allRecords.filter(record => {
    return fields.some(field => {
      const fieldValue = record[field];
      if (!fieldValue) return false;
      
      const value = caseSensitive ? fieldValue : fieldValue.toLowerCase();
      
      switch (matchType) {
        case 'exact':
          return value === searchTerm;
        case 'partial':
          return value.includes(searchTerm);
        case 'fuzzy':
          return fuzzyMatch(value, searchTerm);
        default:
          return value.includes(searchTerm);
      }
    });
  });
}

// البحث الضبابي (Fuzzy Search)
function fuzzyMatch(text, pattern) {
  const patternLength = pattern.length;
  const textLength = text.length;
  
  if (patternLength === 0) return true;
  if (textLength === 0) return false;
  
  let patternIndex = 0;
  
  for (let textIndex = 0; textIndex < textLength && patternIndex < patternLength; textIndex++) {
    if (text[textIndex] === pattern[patternIndex]) {
      patternIndex++;
    }
  }
  
  return patternIndex === patternLength;
}

// البحث بالتعبيرات النمطية
function regexSearch(pattern, flags = 'gi') {
  try {
    const regex = new RegExp(pattern, flags);
    
    return allRecords.filter(record => {
      return regex.test(record.bookNumber) ||
             regex.test(record.subject) ||
             regex.test(record.content) ||
             regex.test(record.department) ||
             (record.notes && regex.test(record.notes));
    });
  } catch (error) {
    console.error('خطأ في التعبير النمطي:', error);
    return [];
  }
}

// تصفية حسب نطاق التواريخ المرن
function filterByDateRange(startDate, endDate, records = allRecords) {
  return records.filter(record => {
    const recordDate = new Date(record.date.year, record.date.month - 1, record.date.day);
    return recordDate >= startDate && recordDate <= endDate;
  });
}

// تصفية حسب الكلمات المفتاحية المتعددة
function filterByKeywords(keywords, records = allRecords) {
  const keywordArray = keywords.split(/\s+/).filter(keyword => keyword.length > 0);
  
  return records.filter(record => {
    const searchText = `${record.bookNumber} ${record.subject} ${record.content} ${record.department} ${record.notes || ''}`.toLowerCase();
    
    return keywordArray.every(keyword => 
      searchText.includes(keyword.toLowerCase())
    );
  });
}

// تجميع البيانات حسب معايير مختلفة
function groupRecords(records, groupBy) {
  const groups = {};
  
  records.forEach(record => {
    let groupKey = '';
    
    switch (groupBy) {
      case 'department':
        groupKey = record.department;
        break;
      case 'year':
        groupKey = record.date.year.toString();
        break;
      case 'month':
        groupKey = `${record.date.month}/${record.date.year}`;
        break;
      case 'subject':
        groupKey = record.subject.substring(0, 20) + '...';
        break;
      default:
        groupKey = 'غير محدد';
    }
    
    if (!groups[groupKey]) {
      groups[groupKey] = [];
    }
    groups[groupKey].push(record);
  });
  
  return groups;
}

// ترتيب النتائج
function sortRecords(records, sortBy, direction = 'asc') {
  return [...records].sort((a, b) => {
    let valueA, valueB;
    
    switch (sortBy) {
      case 'bookNumber':
        valueA = parseInt(a.bookNumber) || 0;
        valueB = parseInt(b.bookNumber) || 0;
        break;
      case 'date':
        valueA = new Date(a.date.year, a.date.month - 1, a.date.day);
        valueB = new Date(b.date.year, b.date.month - 1, b.date.day);
        break;
      case 'subject':
        valueA = a.subject.toLowerCase();
        valueB = b.subject.toLowerCase();
        break;
      case 'department':
        valueA = a.department.toLowerCase();
        valueB = b.department.toLowerCase();
        break;
      case 'createdAt':
        valueA = new Date(a.createdAt);
        valueB = new Date(b.createdAt);
        break;
      default:
        return 0;
    }
    
    if (valueA < valueB) return direction === 'asc' ? -1 : 1;
    if (valueA > valueB) return direction === 'asc' ? 1 : -1;
    return 0;
  });
}

// تصدير النتائج المفلترة
function exportFilteredResults(results, format = 'json') {
  if (results.length === 0) {
    showNotification('لا توجد نتائج للتصدير', 'warning');
    return;
  }
  
  let content, filename, mimeType;
  
  switch (format) {
    case 'json':
      content = JSON.stringify(results, null, 2);
      filename = `filtered_results_${new Date().toISOString().split('T')[0]}.json`;
      mimeType = 'application/json';
      break;
      
    case 'csv':
      content = convertToCSV(results);
      filename = `filtered_results_${new Date().toISOString().split('T')[0]}.csv`;
      mimeType = 'text/csv';
      break;
      
    case 'txt':
      content = convertToText(results);
      filename = `filtered_results_${new Date().toISOString().split('T')[0]}.txt`;
      mimeType = 'text/plain';
      break;
      
    default:
      showNotification('صيغة التصدير غير مدعومة', 'error');
      return;}
  
  const blob = new Blob([content], { type: mimeType });
  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.download = filename;
  link.click();
  
  showNotification(`تم تصدير ${results.length} سجل بصيغة ${format.toUpperCase()}`, 'success');
}

// تحويل البيانات إلى CSV
function convertToCSV(records) {
  const headers = ['رقم الكتاب', 'التاريخ', 'الموضوع', 'الفحوى', 'الدائرة', 'الهامش'];
  const csvContent = [headers.join(',')];
  
  records.forEach(record => {
    const row = [
      record.bookNumber,
      `${record.date.day}/${record.date.month}/${record.date.year}`,
      `"${record.subject}"`,
      `"${record.content}"`,
      `"${record.department}"`,
      `"${record.notes || ''}"`
    ];
    csvContent.push(row.join(','));
  });
  
  return csvContent.join('\n');
}

// تحويل البيانات إلى نص
function convertToText(records) {
  let textContent = 'تقرير السجلات المفلترة\n';
  textContent += '='.repeat(50) + '\n\n';
  
  records.forEach((record, index) => {
    textContent += `السجل ${index + 1}:\n`;
    textContent += `رقم الكتاب: ${record.bookNumber}\n`;
    textContent += `التاريخ: ${record.date.day}/${record.date.month}/${record.date.year}\n`;
    textContent += `الموضوع: ${record.subject}\n`;
    textContent += `الفحوى: ${record.content}\n`;
    textContent += `الدائرة: ${record.department}\n`;
    if (record.notes) {
      textContent += `الهامش: ${record.notes}\n`;
    }
    textContent += '-'.repeat(30) + '\n\n';
  });
  
  return textContent;
}

// إحصائيات البحث والتصفية
function getFilterStatistics(originalRecords, filteredRecords) {
  const stats = {
    totalRecords: originalRecords.length,
    filteredRecords: filteredRecords.length,
    filterRatio: originalRecords.length > 0 ? (filteredRecords.length / originalRecords.length * 100).toFixed(1) : 0,
    departments: {},
    dateRange: {
      earliest: null,
      latest: null
    }
  };
  
  // إحصائيات الدوائر
  filteredRecords.forEach(record => {
    stats.departments[record.department] = (stats.departments[record.department] || 0) + 1;
  });
  
  // نطاق التواريخ
  if (filteredRecords.length > 0) {
    const dates = filteredRecords.map(record => 
      new Date(record.date.year, record.date.month - 1, record.date.day)
    );
    
    stats.dateRange.earliest = new Date(Math.min(...dates));
    stats.dateRange.latest = new Date(Math.max(...dates));
  }
  
  return stats;
}

// حفظ معايير البحث المستخدمة بكثرة
function saveSearchPreset(name, filters) {
  const presets = loadFromStorage('searchPresets') || {};
  presets[name] = {
    filters,
    createdAt: new Date().toISOString(),
    usageCount: (presets[name]?.usageCount || 0) + 1
  };
  
  saveToStorage('searchPresets', presets);
  showNotification(`تم حفظ معايير البحث: ${name}`, 'success');
}

// تحميل معايير البحث المحفوظة
function loadSearchPreset(name) {
  const presets = loadFromStorage('searchPresets') || {};
  return presets[name]?.filters || null;
}

// الحصول على جميع معايير البحث المحفوظة
function getAllSearchPresets() {
  return loadFromStorage('searchPresets') || {};
}

// تطبيق معايير بحث محفوظة
function applySearchPreset(name) {
  const filters = loadSearchPreset(name);
  if (!filters) {
    showNotification('معايير البحث غير موجودة', 'error');
    return;
  }
  
  // ملء النموذج بالمعايير المحفوظة
  const form = document.getElementById('filter-form');
  if (!form) return;
  
  // ملء حقول التاريخ
  if (filters.dateRange.from.day) {
    form.querySelector('input[name="fromDay"]').value = filters.dateRange.from.day;
  }
  if (filters.dateRange.from.month) {
    form.querySelector('input[name="fromMonth"]').value = filters.dateRange.from.month;
  }
  if (filters.dateRange.from.year) {
    form.querySelector('input[name="fromYear"]').value = filters.dateRange.from.year;
  }
  
  if (filters.dateRange.to.day) {
    form.querySelector('input[name="toDay"]').value = filters.dateRange.to.day;
  }
  if (filters.dateRange.to.month) {
    form.querySelector('input[name="toMonth"]').value = filters.dateRange.to.month;
  }
  if (filters.dateRange.to.year) {
    form.querySelector('input[name="toYear"]').value = filters.dateRange.to.year;
  }
  
  // ملء حقول المحتوى
  if (filters.subject) {
    form.querySelector('input[name="filterSubject"]').value = filters.subject;
  }
  if (filters.employee) {
    form.querySelector('input[name="filterEmployee"]').value = filters.employee;
  }
  if (filters.department) {
    form.querySelector('select[name="filterDepartment"]').value = filters.department;
  }
  if (filters.notes) {
    form.querySelector('select[name="filterNotes"]').value = filters.notes;
  }
  
  // تطبيق التصفية
  const filteredResults = applyFilters(allRecords, filters);
  displayFilterResults(filteredResults);
  
  // تحديث عداد الاستخدام
  const presets = loadFromStorage('searchPresets') || {};
  if (presets[name]) {
    presets[name].usageCount++;
    presets[name].lastUsed = new Date().toISOString();
    saveToStorage('searchPresets', presets);
  }
  
  showNotification(`تم تطبيق معايير البحث: ${name}`, 'success');
}

// إدارة سجل البحث
const SearchHistory = {
  add(query, results) {
    const history = this.get();
    const entry = {
      id: Date.now().toString(),
      query,
      resultsCount: results.length,
      timestamp: new Date().toISOString()
    };
    
    history.unshift(entry);
    
    // الاحتفاظ بآخر 50 بحث فقط
    if (history.length > 50) {
      history.splice(50);
    }
    
    saveToStorage('searchHistory', history);
  },
  
  get() {
    return loadFromStorage('searchHistory') || [];
  },
  
  clear() {
    removeFromStorage('searchHistory');
    showNotification('تم مسح سجل البحث', 'info');
  },
  
  remove(id) {
    const history = this.get();
    const index = history.findIndex(entry => entry.id === id);
    if (index !== -1) {
      history.splice(index, 1);
      saveToStorage('searchHistory', history);
    }
  }
};

// تصدير وظائف البحث والتصفية
window.FilterManager = {
  apply: applyFilters,
  advanced: advancedSearch,
  regex: regexSearch,
  dateRange: filterByDateRange,
  keywords: filterByKeywords,
  group: groupRecords,
  sort: sortRecords,
  export: exportFilteredResults,
  stats: getFilterStatistics,
  presets: {
    save: saveSearchPreset,
    load: loadSearchPreset,
    getAll: getAllSearchPresets,
    apply: applySearchPreset
  },
  history: SearchHistory
};