// متغيرات عامة
let currentRecordIndex = 0;
let allRecords = [];
let filteredRecords = [];

// تهيئة البرنامج
document.addEventListener('DOMContentLoaded', function() {
  loadRecordsFromStorage();
  updateReportTable();
  updateRecordDisplay();
  populateDepartmentFilter();
  applyStoredTheme();
});

// عرض الواجهات
function showScreen(screenId) {
  // إخفاء جميع الواجهات
  const screens = document.querySelectorAll('.screen');
  screens.forEach(screen => {
    screen.classList.remove('active');
  });
  
  // عرض الواجهة المطلوبة
  const targetScreen = document.getElementById(screenId);
  if (targetScreen) {
    targetScreen.classList.add('active');
    targetScreen.classList.add('fade-in');
    
    // تحديث البيانات حسب الواجهة
    switch(screenId) {
      case 'report':
        updateReportTable();
        break;
      case 'archive':
        updateRecordDisplay();
        break;
      case 'filter':
        populateDepartmentFilter();
        break;
    }
  }
}

// إدخال البيانات الجديدة
document.getElementById('dataEntryForm').addEventListener('submit', function(e) {
  e.preventDefault();
  
  const formData = new FormData(e.target);
  const record = {
    id: Date.now(),
    bookNumber: formData.get('bookNumber'),
    day: formData.get('day'),
    month: formData.get('month'),
    year: formData.get('year'),
    subject: formData.get('subject'),
    content: formData.get('content'),
    department: formData.get('department'),
    notes: formData.get('notes') || '',
    dateCreated: new Date().toISOString()
  };
  
  // التحقق من صحة البيانات
  if (!validateRecord(record)) {
    showMessage('يرجى ملء جميع الحقول المطلوبة بشكل صحيح', 'error');
    return;
  }
  
  // حفظ السجل
  allRecords.push(record);
  saveRecordsToStorage();
  
  // عرض رسالة النجاح
  showMessage('تم حفظ البيانات بنجاح', 'success');
  
  // مسح النموذج
  e.target.reset();
  
  // تحديث التقرير والأرشيف
  updateReportTable();
  updateRecordDisplay();
});

// التحقق من صحة البيانات
function validateRecord(record) {
  // التحقق من الحقول المطلوبة
  if (!record.bookNumber || !record.day || !record.month || !record.year || 
      !record.subject || !record.content || !record.department) {
    return false;
  }
  
  // التحقق من صحة التاريخ
  const day = parseInt(record.day);
  const month = parseInt(record.month);
  const year = parseInt(record.year);
  
  if (day < 1 || day > 31 || month < 1 || month > 12 || year < 2000 || year > 2100) {
    return false;
  }
  
  return true;
}

// تحديث جدول التقرير
function updateReportTable() {
  const tableBody = document.getElementById('reportTableBody');
  tableBody.innerHTML = '';
  
  if (allRecords.length === 0) {
    tableBody.innerHTML = '<tr><td colspan="6" style="text-align: center;">لا توجد بيانات مسجلة</td></tr>';
    return;
  }
  
  allRecords.forEach(record => {
    const row = document.createElement('tr');
    row.innerHTML = `
      <td>${record.bookNumber}</td>
      <td>${record.day}/${record.month}/${record.year}</td>
      <td>${record.subject}</td>
      <td>${record.content}</td>
      <td>${record.department}</td>
      <td>${record.notes}</td>
    `;
    tableBody.appendChild(row);
  });
}

// تحديث عرض السجل في الأرشيف
function updateRecordDisplay() {
  const recordDisplay = document.getElementById('recordDisplay');
  
  if (allRecords.length === 0) {
    recordDisplay.innerHTML = '<p style="text-align: center; color: #666;">لا توجد سجلات في الأرشيف</p>';
    return;
  }
  
  if (currentRecordIndex >= allRecords.length) {
    currentRecordIndex = allRecords.length - 1;
  }
  
  if (currentRecordIndex < 0) {
    currentRecordIndex = 0;
  }
  
  const record = allRecords[currentRecordIndex];
  recordDisplay.innerHTML = `
    <div class="record-item">
      <h3>سجل رقم ${currentRecordIndex + 1} من ${allRecords.length}</h3>
      <div class="record-details">
        <div class="detail-row">
          <strong>رقم الكتاب:</strong> ${record.bookNumber}
        </div>
        <div class="detail-row">
          <strong>التاريخ:</strong> ${record.day}/${record.month}/${record.year}
        </div>
        <div class="detail-row">
          <strong>الموضوع:</strong> ${record.subject}
        </div>
        <div class="detail-row">
          <strong>الفحوى/الموظف المعني:</strong> ${record.content}
        </div>
        <div class="detail-row">
          <strong>الدائرة المعنية:</strong> ${record.department}
        </div>
        <div class="detail-row">
          <strong>الهامش:</strong> ${record.notes || 'لا يوجد'}
        </div>
      </div>
    </div>
  `;
}

// التنقل في الأرشيف
function firstRecord() {
  currentRecordIndex = 0;
  updateRecordDisplay();
}

function lastRecord() {
  currentRecordIndex = allRecords.length - 1;
  updateRecordDisplay();
}

function nextRecord() {
  if (currentRecordIndex < allRecords.length - 1) {
    currentRecordIndex++;
    updateRecordDisplay();
  }
}

function previousRecord() {
  if (currentRecordIndex > 0) {
    currentRecordIndex--;
    updateRecordDisplay();
  }
}

// البحث في السجلات
function searchRecords() {
  const searchTerm = document.getElementById('searchInput').value.toLowerCase().trim();
  
  if (!searchTerm) {
    showMessage('يرجى إدخال كلمة للبحث', 'error');
    return;
  }
  
  const results = allRecords.filter(record => {
    return record.bookNumber.toLowerCase().includes(searchTerm) ||
           record.subject.toLowerCase().includes(searchTerm) ||
           record.content.toLowerCase().includes(searchTerm) ||
           record.department.toLowerCase().includes(searchTerm) ||
           record.notes.toLowerCase().includes(searchTerm);
  });
  
  if (results.length === 0) {
    showMessage('لم يتم العثور على نتائج', 'error');
    return;
  }
  
  // عرض النتائج في الأرشيف
  allRecords = results;
  currentRecordIndex = 0;
  updateRecordDisplay();
  showMessage(`تم العثور على ${results.length} نتيجة`, 'success');
}

// تعديل السجل
function editRecord() {
  if (allRecords.length === 0) {
    showMessage('لا توجد سجلات للتعديل', 'error');
    return;
  }
  
  const record = allRecords[currentRecordIndex];
  
  // ملء النموذج بالبيانات الحالية
  document.getElementById('bookNumber').value = record.bookNumber;
  document.getElementById('day').value = record.day;
  document.getElementById('month').value = record.month;
  document.getElementById('year').value = record.year;
  document.getElementById('subject').value = record.subject;
  document.getElementById('content').value = record.content;
  document.getElementById('department').value = record.department;
  document.getElementById('notes').value = record.notes;
  
  // حذف السجل القديم
  allRecords.splice(currentRecordIndex, 1);
  saveRecordsToStorage();
  
  // الانتقال إلى واجهة الإدخال
  showScreen('dataEntry');
  showMessage('تم تحميل البيانات للتعديل', 'success');
}

// حذف السجل
function deleteRecord() {
  if (allRecords.length === 0) {
    showMessage('لا توجد سجلات للحذف', 'error');
    return;
  }
  
  if (confirm('هل أنت متأكد من حذف هذا السجل؟')) {
    allRecords.splice(currentRecordIndex, 1);
    saveRecordsToStorage();
    
    if (currentRecordIndex >= allRecords.length) {
      currentRecordIndex = allRecords.length - 1;
    }
    
    updateRecordDisplay();
    updateReportTable();
    showMessage('تم حذف السجل بنجاح', 'success');
  }
}

// تطبيق التصفية
function applyFilter() {
  const fromDay = document.getElementById('fromDay').value;
  const fromMonth = document.getElementById('fromMonth').value;
  const fromYear = document.getElementById('fromYear').value;
  const toDay = document.getElementById('toDay').value;
  const toMonth = document.getElementById('toMonth').value;
  const toYear = document.getElementById('toYear').value;
  const filterSubject = document.getElementById('filterSubject').value.toLowerCase();
  const filterEmployee = document.getElementById('filterEmployee').value.toLowerCase();
  const filterDepartment = document.getElementById('filterDepartment').value;
  const filterNotes = document.getElementById('filterNotes').value;
  
  filteredRecords = allRecords.filter(record => {
    // تصفية التاريخ
    if (fromYear && toYear) {
      const recordDate = new Date(record.year, record.month - 1, record.day);
      const fromDate = new Date(fromYear, (fromMonth || 1) - 1, fromDay || 1);
      const toDate = new Date(toYear, (toMonth || 12) - 1, toDay || 31);
      
      if (recordDate < fromDate || recordDate > toDate) {
        return false;
      }
    }
    
    // تصفية الموضوع
    if (filterSubject && !record.subject.toLowerCase().includes(filterSubject)) {
      return false;
    }
    
    // تصفية الموظف
    if (filterEmployee && !record.content.toLowerCase().includes(filterEmployee)) {
      return false;
    }
    
    // تصفية الدائرة
    if (filterDepartment && record.department !== filterDepartment) {
      return false;
    }
    
    // تصفية الهامش
    if (filterNotes === 'has-notes' && !record.notes) {
      return false;
    }
    
    if (filterNotes === 'no-notes' && record.notes) {
      return false;
    }
    
    return true;
  });
  
  displayFilterResults();
}

// عرض نتائج التصفية
function displayFilterResults() {
  const resultsDiv = document.getElementById('filterResults');
  
  if (filteredRecords.length === 0) {
    resultsDiv.innerHTML = '<p style="text-align: center; color: #666;">لا توجد نتائج تطابق معايير التصفية</p>';
    return;
  }
  
  let html = `<h3>نتائج التصفية (${filteredRecords.length} سجل)</h3>
              <div class="filtered-results">`;
  
  filteredRecords.forEach((record, index) => {
    html += `
      <div class="filter-result-item">
        <div class="result-header">
          <strong>السجل ${index + 1} - رقم الكتاب: ${record.bookNumber}</strong>
          <span class="result-date">${record.day}/${record.month}/${record.year}</span>
        </div>
        <div class="result-content">
          <p><strong>الموضوع:</strong> ${record.subject}</p>
          <p><strong>الفحوى:</strong> ${record.content}</p>
          <p><strong>الدائرة:</strong> ${record.department}</p>
          ${record.notes ? `<p><strong>الهامش:</strong> ${record.notes}</p>` : ''}
        </div>
      </div>
    `;
  });
  
  html += '</div>';
  resultsDiv.innerHTML = html;
}

// ملء قائمة الدوائر في التصفية
function populateDepartmentFilter() {
  const select = document.getElementById('filterDepartment');
  const departments = [...new Set(allRecords.map(record => record.department))];
  
  // مسح الخيارات الحالية
  select.innerHTML = '<option value="">جميع الدوائر</option>';
  
  // إضافة الدوائر
  departments.forEach(dept => {
    const option = document.createElement('option');
    option.value = dept;
    option.textContent = dept;
    select.appendChild(option);
  });
}

// تصدير البيانات
function exportData() {
  if (allRecords.length === 0) {
    showMessage('لا توجد بيانات للتصدير', 'error');
    return;
  }
  
  const dataStr = JSON.stringify(allRecords, null, 2);
  const dataBlob = new Blob([dataStr], {type: 'application/json'});
  
  const link = document.createElement('a');
  link.href = URL.createObjectURL(dataBlob);
  link.download = `archive_data_${new Date().toISOString().split('T')[0]}.json`;
  link.click();
  
  showMessage('تم تصدير البيانات بنجاح', 'success');
}

// استيراد البيانات
function importData() {
  const input = document.createElement('input');
  input.type = 'file';
  input.accept = '.json';
  
  input.onchange = function(e) {
    const file = e.target.files[0];
    if (!file) return;
    
    const reader = new FileReader();
    reader.onload = function(e) {
      try {
        const importedData = JSON.parse(e.target.result);
        
        if (!Array.isArray(importedData)) {
          throw new Error('تنسيق الملف غير صحيح');
        }
        
        allRecords = importedData;
        saveRecordsToStorage();
        updateReportTable();
        updateRecordDisplay();
        populateDepartmentFilter();
        
        showMessage(`تم استيراد ${importedData.length} سجل بنجاح`, 'success');
      } catch (error) {
        showMessage('خطأ في قراءة الملف', 'error');
      }
    };
    
    reader.readAsText(file);
  };
  
  input.click();
}

// مسح جميع البيانات
function clearAllData() {
  if (confirm('هل أنت متأكد من حذف جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه.')) {
    if (confirm('تأكيد نهائي: سيتم حذف جميع السجلات نهائياً')) {
      allRecords = [];
      filteredRecords = [];
      currentRecordIndex = 0;
      
      saveRecordsToStorage();
      updateReportTable();
      updateRecordDisplay();
      populateDepartmentFilter();
      
      showMessage('تم حذف جميع البيانات', 'success');
    }
  }
}

// عرض الرسائل
function showMessage(message, type) {
  // إنشاء عنصر الرسالة
  const messageDiv = document.createElement('div');
  messageDiv.className = `message ${type}-message`;
  messageDiv.textContent = message;
  messageDiv.style.display = 'block';
  messageDiv.style.position = 'fixed';
  messageDiv.style.top = '20px';
  messageDiv.style.right = '20px';
  messageDiv.style.zIndex = '1000';
  messageDiv.style.minWidth = '300px';
  messageDiv.style.borderRadius = '10px';
  messageDiv.style.padding = '1rem';
  messageDiv.style.boxShadow = '0 5px 15px rgba(0,0,0,0.3)';
  
  document.body.appendChild(messageDiv);
  
  // إزالة الرسالة بعد 3 ثوان
  setTimeout(() => {
    if (messageDiv.parentNode) {
      messageDiv.parentNode.removeChild(messageDiv);
    }
  }, 3000);
}

// إضافة أنماط CSS للرسائل المفلترة
const style = document.createElement('style');
style.textContent = `
  .filtered-results {
    max-height: 400px;
    overflow-y: auto;
  }
  
  .filter-result-item {
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    margin-bottom: 1rem;
    padding: 1rem;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
  }
  
  .result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #eee;
  }
  
  .result-date {
    color: #666;
    font-size: 0.9rem;
  }
  
  .result-content p {
    margin: 0.25rem 0;
    line-height: 1.4;
  }
  
  .record-details {
    line-height: 1.6;
  }
  
  .detail-row {
    margin-bottom: 1rem;
    display: flex;
    flex-wrap: wrap;
  }
  
  .detail-row strong {
    min-width: 150px;
    color: var(--primary-color);
  }
`;
document.head.appendChild(style);