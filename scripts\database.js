// قاعدة البيانات المحلية المتقدمة
class LocalDatabase {
  constructor() {
    this.dbName = 'electronic_archive_db';
    this.version = 1;
    this.data = [];
    this.loadData();
  }

  // تحميل البيانات من نظام الملفات
  async loadData() {
    try {
      // استخدام واجهة Electron للتخزين المحلي إذا كانت متوفرة
      if (window.electronAPI) {
        this.data = await window.electronAPI.loadData();
      } else {
        // استخدام localStorage كبديل
        const stored = localStorage.getItem(this.dbName);
        this.data = stored ? JSON.parse(stored) : [];
      }
      return this.data;
    } catch (error) {
      console.error('خطأ في تحميل البيانات:', error);
      // استخدام localStorage كبديل في حالة الخطأ
      const stored = localStorage.getItem(this.dbName);
      this.data = stored ? JSON.parse(stored) : [];
      return this.data;
    }
  }

  // حفظ البيانات في نظام الملفات
  async saveData(data) {
    try {
      this.data = data;
      
      // استخدام واجهة Electron للتخزين المحلي إذا كانت متوفرة
      if (window.electronAPI) {
        await window.electronAPI.saveData(data);
      }
      
      // حفظ في localStorage أيضاً كنسخة احتياطية
      localStorage.setItem(this.dbName, JSON.stringify(data));
      return true;
    } catch (error) {
      console.error('خطأ في حفظ البيانات:', error);
      // محاولة الحفظ في localStorage على الأقل
      try {
        localStorage.setItem(this.dbName, JSON.stringify(data));
      } catch (localError) {
        console.error('خطأ في الحفظ الاحتياطي:', localError);
      }
      return false;
    }
  }

  // إضافة سجل جديد
  addRecord(record) {
    record.id = this.generateId();
    record.createdAt = new Date().toISOString();
    record.updatedAt = record.createdAt;

    this.data.push(record);
    return this.saveData(this.data);
  }

  // تحديث سجل موجود
  updateRecord(id, updatedRecord) {
    const index = this.data.findIndex(record => record.id === id);
    if (index !== -1) {
      updatedRecord.id = id;
      updatedRecord.updatedAt = new Date().toISOString();
      this.data[index] = { ...this.data[index], ...updatedRecord };
      return this.saveData(this.data);
    }
    return false;
  }

  // حذف سجل
  deleteRecord(id) {
    const initialLength = this.data.length;
    this.data = this.data.filter(record => record.id !== id);
    if (this.data.length < initialLength) {
      return this.saveData(this.data);
    }
    return false;
  }

  // البحث في السجلات
  searchRecords(query) {
    const searchTerm = query.toLowerCase();
    return this.data.filter(record => 
      Object.values(record).some(value => 
        value && value.toString().toLowerCase().includes(searchTerm)
      )
    );
  }

  // تصفية السجلات
  filterRecords(filters) {
    return this.data.filter(record => {
      for (const [key, value] of Object.entries(filters)) {
        if (value && record[key] !== value) {
          return false;
        }
      }
      return true;
    });
  }

  // الحصول على جميع السجلات
  getAllRecords() {
    return [...this.data];
  }

  // الحصول على سجل بالمعرف
  getRecordById(id) {
    return this.data.find(record => record.id === id);
  }

  // توليد معرف فريد
  generateId() {
    return Date.now() + Math.random().toString(36).substr(2, 9);
  }

  // نسخ احتياطي للبيانات
  backup() {
    const backup = {
      version: this.version,
      timestamp: new Date().toISOString(),
      data: this.data
    };
    return JSON.stringify(backup, null, 2);
  }

  // استعادة من نسخة احتياطية
  restore(backupData) {
    try {
      const parsed = JSON.parse(backupData);
      if (parsed.data && Array.isArray(parsed.data)) {
        this.data = parsed.data;
        return this.saveData(this.data);
      }
      return false;
    } catch (error) {
      console.error('خطأ في استعادة البيانات:', error);
      return false;
    }
  }

  // إحصائيات قاعدة البيانات
  getStatistics() {
    const totalRecords = this.data.length;
    const recordsWithNotes = this.data.filter(r => r.notes && r.notes.trim()).length;
    const departments = [...new Set(this.data.map(r => r.department))].filter(Boolean);

    // إحصائيات سنوية
    const yearlyStats = {};
    this.data.forEach(record => {
      const year = record.year || new Date(record.createdAt).getFullYear();
      yearlyStats[year] = (yearlyStats[year] || 0) + 1;
    });

    return {
      totalRecords,
      recordsWithNotes,
      departmentCount: departments.length,
      departments,
      yearlyStats,
      lastUpdated: this.data.length > 0 ? 
        Math.max(...this.data.map(r => new Date(r.updatedAt || r.createdAt).getTime())) : null
    };
  }

  // ضغط قاعدة البيانات (إزالة السجلات المحذوفة)
  compress() {
    const originalSize = this.data.length;
    this.data = this.data.filter(record => !record.deleted);
    const newSize = this.data.length;

    if (originalSize !== newSize) {
      this.saveData(this.data);
      return originalSize - newSize;
    }
    return 0;
  }

  // تصدير البيانات بتنسيقات مختلفة
  exportToCSV() {
    if (this.data.length === 0) return '';

    const headers = ['رقم الكتاب', 'التاريخ', 'الموضوع', 'المحتوى', 'الدائرة', 'الهامش'];
    const csvContent = [
      headers.join(','),
      ...this.data.map(record => [
        record.bookNumber || '',
        record.date || '',
        record.subject || '',
        record.content || '',
        record.department || '',
        record.notes || ''
      ].map(field => `"${field.toString().replace(/"/g, '""')}"`).join(','))
    ].join('\n');

    return csvContent;
  }

  // تنظيف قاعدة البيانات
  cleanup() {
    // إزالة السجلات المكررة
    const unique = [];
    const seen = new Set();

    for (const record of this.data) {
      const key = `${record.bookNumber}-${record.date}-${record.subject}`;
      if (!seen.has(key)) {
        seen.add(key);
        unique.push(record);
      }
    }

    if (unique.length !== this.data.length) {
      this.data = unique;
      this.saveData(this.data);
      return this.data.length - unique.length;
    }

    return 0;
  }
}

// إنشاء مثيل من قاعدة البيانات
const db = new LocalDatabase();
