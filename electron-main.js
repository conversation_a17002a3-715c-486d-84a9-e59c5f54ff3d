// ملف Electron الرئيسي لدعم التخزين في القرص D
// المطور: المحاسب المبرمج علي عاجل خشان المحنة

const { app, BrowserWindow, ipcMain, dialog } = require('electron');
const path = require('path');
const fs = require('fs');

// مسار التخزين في القرص D
const STORAGE_PATH = 'D:/ArchiveData/';

let mainWindow;

function createWindow() {
  // إنشاء النافذة الرئيسية
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'electron-preload.js')
    },
    icon: path.join(__dirname, 'assets/icon.png'),
    title: 'برنامج الأرشفة الإلكترونية - المطور: المحاسب المبرمج علي عاجل خشان المحنة'
  });

  // تحميل الملف الرئيسي
  mainWindow.loadFile('index.html');

  // إنشاء مجلد التخزين إذا لم يكن موجوداً
  createStorageFolder();
}

// إنشاء مجلد التخزين
function createStorageFolder() {
  try {
    if (!fs.existsSync(STORAGE_PATH)) {
      fs.mkdirSync(STORAGE_PATH, { recursive: true });
      console.log('تم إنشاء مجلد التخزين:', STORAGE_PATH);
    }
  } catch (error) {
    console.error('خطأ في إنشاء مجلد التخزين:', error);
  }
}

// حفظ البيانات في القرص D
ipcMain.handle('save-data', async (event, data) => {
  try {
    const filePath = path.join(STORAGE_PATH, 'archive_data.json');
    const dataToSave = {
      saveDate: new Date().toISOString(),
      developer: 'المحاسب المبرمج علي عاجل خشان المحنة',
      version: '2.0.0',
      data: data
    };
    
    fs.writeFileSync(filePath, JSON.stringify(dataToSave, null, 2), 'utf8');
    console.log('تم حفظ البيانات في:', filePath);
    return { success: true, path: filePath };
  } catch (error) {
    console.error('خطأ في حفظ البيانات:', error);
    return { success: false, error: error.message };
  }
});

// تحميل البيانات من القرص D
ipcMain.handle('load-data', async () => {
  try {
    const filePath = path.join(STORAGE_PATH, 'archive_data.json');
    
    if (fs.existsSync(filePath)) {
      const fileContent = fs.readFileSync(filePath, 'utf8');
      const parsedData = JSON.parse(fileContent);
      return parsedData.data || [];
    }
    
    return [];
  } catch (error) {
    console.error('خطأ في تحميل البيانات:', error);
    return [];
  }
});

// حفظ ملف في مسار محدد
ipcMain.handle('save-file', async (event, filePath, content) => {
  try {
    const dir = path.dirname(filePath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    
    fs.writeFileSync(filePath, content, 'utf8');
    return { success: true, path: filePath };
  } catch (error) {
    console.error('خطأ في حفظ الملف:', error);
    return { success: false, error: error.message };
  }
});

// قراءة ملف من مسار محدد
ipcMain.handle('load-file', async (event, filePath) => {
  try {
    if (fs.existsSync(filePath)) {
      return fs.readFileSync(filePath, 'utf8');
    }
    return null;
  } catch (error) {
    console.error('خطأ في قراءة الملف:', error);
    return null;
  }
});

// اختيار ملف للاستيراد
ipcMain.handle('select-file', async () => {
  try {
    const result = await dialog.showOpenDialog(mainWindow, {
      properties: ['openFile'],
      filters: [
        { name: 'JSON Files', extensions: ['json'] },
        { name: 'All Files', extensions: ['*'] }
      ]
    });
    
    if (!result.canceled && result.filePaths.length > 0) {
      return result.filePaths[0];
    }
    
    return null;
  } catch (error) {
    console.error('خطأ في اختيار الملف:', error);
    return null;
  }
});

// اختيار مجلد للحفظ
ipcMain.handle('select-folder', async () => {
  try {
    const result = await dialog.showOpenDialog(mainWindow, {
      properties: ['openDirectory']
    });
    
    if (!result.canceled && result.filePaths.length > 0) {
      return result.filePaths[0];
    }
    
    return null;
  } catch (error) {
    console.error('خطأ في اختيار المجلد:', error);
    return null;
  }
});

// إنشاء نسخة احتياطية
ipcMain.handle('create-backup', async (event, data) => {
  try {
    const timestamp = new Date().toISOString().split('T')[0];
    const backupFileName = `archive_backup_${timestamp}.json`;
    const backupPath = path.join(STORAGE_PATH, 'backups');
    
    if (!fs.existsSync(backupPath)) {
      fs.mkdirSync(backupPath, { recursive: true });
    }
    
    const fullBackupPath = path.join(backupPath, backupFileName);
    const backupData = {
      backupDate: new Date().toISOString(),
      developer: 'المحاسب المبرمج علي عاجل خشان المحنة',
      version: '2.0.0',
      totalRecords: data.length,
      data: data
    };
    
    fs.writeFileSync(fullBackupPath, JSON.stringify(backupData, null, 2), 'utf8');
    return { success: true, path: fullBackupPath };
  } catch (error) {
    console.error('خطأ في إنشاء النسخة الاحتياطية:', error);
    return { success: false, error: error.message };
  }
});

// الحصول على معلومات النظام
ipcMain.handle('get-system-info', async () => {
  try {
    const os = require('os');
    return {
      platform: os.platform(),
      arch: os.arch(),
      totalMemory: os.totalmem(),
      freeMemory: os.freemem(),
      cpus: os.cpus().length,
      storagePath: STORAGE_PATH,
      storageExists: fs.existsSync(STORAGE_PATH)
    };
  } catch (error) {
    console.error('خطأ في الحصول على معلومات النظام:', error);
    return null;
  }
});

// إعداد التطبيق
app.whenReady().then(() => {
  createWindow();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// معالجة الأخطاء
process.on('uncaughtException', (error) => {
  console.error('خطأ غير متوقع:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('رفض غير معالج:', reason);
});
