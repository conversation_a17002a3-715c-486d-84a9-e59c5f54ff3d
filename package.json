{"name": "electronic-archiving-system", "version": "1.0.0", "description": "نظام أرشفة إلكترونية شامل لإدارة الوثائق والمستندات الرسمية", "main": "main.js", "keywords": ["archive", "document-management", "electronic-filing", "arabic", "desktop-app", "file-management"], "author": "علي عاجل خشان المحنة", "license": "MIT", "scripts": {"start": "electron .", "dev": "cross-env NODE_ENV=development electron .", "build": "electron-builder", "pack": "electron-builder --dir", "dist": "electron-builder"}, "build": {"appId": "com.electronic.archiving", "productName": "برنامج الأرشفة الإلكترونية", "directories": {"output": "dist"}, "files": ["**/*", "!dist/**/*", "!node_modules/**/*"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "برنامج الأرشفة الإلكترونية"}}, "repository": {"type": "git", "url": "https://github.com/your-username/electronic-archiving-system.git"}, "bugs": {"url": "https://github.com/your-username/electronic-archiving-system/issues"}, "homepage": "https://github.com/your-username/electronic-archiving-system#readme", "dependencies": {"electron-store": "^8.1.0"}, "devDependencies": {"cross-env": "^7.0.3", "electron": "^25.3.1", "electron-builder": "^24.4.0"}, "engines": {"node": ">=14.0.0"}}