// ملف التوافق والكشف عن ميزات المتصفح
(function() {
  'use strict';

  // كائن لتتبع ميزات المتصفح
  window.BrowserSupport = {
    features: {},
    warnings: [],
    
    // فحص الميزات الأساسية
    checkBasicFeatures: function() {
      this.features.localStorage = this.checkLocalStorage();
      this.features.sessionStorage = this.checkSessionStorage();
      this.features.json = this.checkJSON();
      this.features.console = this.checkConsole();
      this.features.addEventListener = this.checkEventListener();
      this.features.querySelector = this.checkQuerySelector();
      this.features.classList = this.checkClassList();
      this.features.dataset = this.checkDataset();
    },
    
    // فحص الميزات المتقدمة
    checkAdvancedFeatures: function() {
      this.features.flexbox = this.checkFlexbox();
      this.features.grid = this.checkGrid();
      this.features.cssVariables = this.checkCSSVariables();
      this.features.fetch = this.checkFetch();
      this.features.promise = this.checkPromise();
      this.features.arrow = this.checkArrowFunctions();
      this.features.const = this.checkConstLet();
      this.features.serviceWorker = this.checkServiceWorker();
      this.features.webWorker = this.checkWebWorker();
      this.features.geolocation = this.checkGeolocation();
      this.features.notification = this.checkNotification();
    },
    
    // فحص localStorage
    checkLocalStorage: function() {
      try {
        var test = 'test';
        localStorage.setItem(test, test);
        localStorage.removeItem(test);
        return true;
      } catch(e) {
        return false;
      }
    },
    
    // فحص sessionStorage
    checkSessionStorage: function() {
      try {
        var test = 'test';
        sessionStorage.setItem(test, test);
        sessionStorage.removeItem(test);
        return true;
      } catch(e) {
        return false;
      }
    },
    
    // فحص JSON
    checkJSON: function() {
      return typeof JSON !== 'undefined' && JSON.parse && JSON.stringify;
    },
    
    // فحص console
    checkConsole: function() {
      return typeof console !== 'undefined' && console.log;
    },
    
    // فحص addEventListener
    checkEventListener: function() {
      return typeof document.addEventListener === 'function';
    },
    
    // فحص querySelector
    checkQuerySelector: function() {
      return typeof document.querySelector === 'function';
    },
    
    // فحص classList
    checkClassList: function() {
      return 'classList' in document.createElement('div');
    },
    
    // فحص dataset
    checkDataset: function() {
      return 'dataset' in document.createElement('div');
    },
    
    // فحص Flexbox
    checkFlexbox: function() {
      try {
        return CSS.supports('display', 'flex');
      } catch(e) {
        var element = document.createElement('div');
        element.style.display = 'flex';
        return element.style.display === 'flex';
      }
    },
    
    // فحص CSS Grid
    checkGrid: function() {
      try {
        return CSS.supports('display', 'grid');
      } catch(e) {
        var element = document.createElement('div');
        element.style.display = 'grid';
        return element.style.display === 'grid';
      }
    },
    
    // فحص CSS Variables
    checkCSSVariables: function() {
      try {
        return CSS.supports('color', 'var(--test)');
      } catch(e) {
        return false;
      }
    },
    
    // فحص Fetch API
    checkFetch: function() {
      return typeof fetch === 'function';
    },
    
    // فحص Promise
    checkPromise: function() {
      return typeof Promise === 'function';
    },
    
    // فحص Arrow Functions
    checkArrowFunctions: function() {
      try {
        eval('() => {}');
        return true;
      } catch(e) {
        return false;
      }
    },
    
    // فحص const/let
    checkConstLet: function() {
      try {
        eval('const test = 1; let test2 = 2;');
        return true;
      } catch(e) {
        return false;
      }
    },
    
    // فحص Service Worker
    checkServiceWorker: function() {
      return 'serviceWorker' in navigator;
    },
    
    // فحص Web Worker
    checkWebWorker: function() {
      return typeof Worker === 'function';
    },
    
    // فحص Geolocation
    checkGeolocation: function() {
      return 'geolocation' in navigator;
    },
    
    // فحص Notification
    checkNotification: function() {
      return 'Notification' in window;
    },
    
    // تشغيل جميع الفحوصات
    runAllChecks: function() {
      this.checkBasicFeatures();
      this.checkAdvancedFeatures();
      this.generateWarnings();
      return this.features;
    },
    
    // إنشاء التحذيرات
    generateWarnings: function() {
      this.warnings = [];
      
      if (!this.features.localStorage) {
        this.warnings.push('localStorage غير مدعوم - قد لا يتم حفظ البيانات');
      }
      
      if (!this.features.json) {
        this.warnings.push('JSON غير مدعوم - قد تحدث أخطاء في معالجة البيانات');
      }
      
      if (!this.features.addEventListener) {
        this.warnings.push('addEventListener غير مدعوم - قد لا تعمل بعض التفاعلات');
      }
      
      if (!this.features.querySelector) {
        this.warnings.push('querySelector غير مدعوم - قد تحدث أخطاء في الواجهة');
      }
      
      if (!this.features.flexbox) {
        this.warnings.push('Flexbox غير مدعوم - قد يظهر التصميم بشكل غير صحيح');
      }
      
      if (!this.features.grid) {
        this.warnings.push('CSS Grid غير مدعوم - قد يظهر التصميم بشكل مختلف');
      }
      
      if (!this.features.cssVariables) {
        this.warnings.push('CSS Variables غير مدعوم - قد لا تعمل الثيمات بشكل صحيح');
      }
    },
    
    // عرض التحذيرات
    showWarnings: function() {
      if (this.warnings.length > 0) {
        var message = 'تحذيرات التوافق:\n\n' + this.warnings.join('\n');
        message += '\n\nيُنصح بتحديث المتصفح للحصول على أفضل تجربة.';
        
        setTimeout(function() {
          alert(message);
        }, 1000);
      }
    },
    
    // الحصول على معلومات المتصفح
    getBrowserInfo: function() {
      var ua = navigator.userAgent;
      var browser = {
        name: 'غير معروف',
        version: 'غير معروف',
        os: 'غير معروف'
      };
      
      // تحديد المتصفح
      if (ua.indexOf('Chrome') > -1) {
        browser.name = 'Chrome';
        browser.version = ua.match(/Chrome\/(\d+)/)[1];
      } else if (ua.indexOf('Firefox') > -1) {
        browser.name = 'Firefox';
        browser.version = ua.match(/Firefox\/(\d+)/)[1];
      } else if (ua.indexOf('Safari') > -1) {
        browser.name = 'Safari';
        browser.version = ua.match(/Version\/(\d+)/)[1];
      } else if (ua.indexOf('Edge') > -1) {
        browser.name = 'Edge';
        browser.version = ua.match(/Edge\/(\d+)/)[1];
      } else if (ua.indexOf('MSIE') > -1 || ua.indexOf('Trident') > -1) {
        browser.name = 'Internet Explorer';
        browser.version = ua.match(/(?:MSIE |rv:)(\d+)/)[1];
      }
      
      // تحديد نظام التشغيل
      if (ua.indexOf('Windows') > -1) {
        browser.os = 'Windows';
      } else if (ua.indexOf('Mac') > -1) {
        browser.os = 'macOS';
      } else if (ua.indexOf('Linux') > -1) {
        browser.os = 'Linux';
      } else if (ua.indexOf('Android') > -1) {
        browser.os = 'Android';
      } else if (ua.indexOf('iOS') > -1) {
        browser.os = 'iOS';
      }
      
      return browser;
    },
    
    // إضافة fallbacks للميزات المفقودة
    addFallbacks: function() {
      // Fallback لـ console
      if (!this.features.console) {
        window.console = {
          log: function() {},
          warn: function() {},
          error: function() {}
        };
      }
      
      // Fallback لـ addEventListener
      if (!this.features.addEventListener) {
        Element.prototype.addEventListener = function(event, handler) {
          this.attachEvent('on' + event, handler);
        };
      }
      
      // Fallback لـ querySelector
      if (!this.features.querySelector) {
        document.querySelector = function(selector) {
          if (selector.charAt(0) === '#') {
            return document.getElementById(selector.slice(1));
          }
          return null;
        };
      }
      
      // Fallback لـ classList
      if (!this.features.classList) {
        Element.prototype.addClass = function(className) {
          this.className += ' ' + className;
        };
        Element.prototype.removeClass = function(className) {
          this.className = this.className.replace(new RegExp(className, 'g'), '');
        };
      }
    }
  };
  
  // تشغيل الفحوصات عند تحميل الصفحة
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
      window.BrowserSupport.runAllChecks();
      window.BrowserSupport.addFallbacks();
      window.BrowserSupport.showWarnings();
    });
  } else {
    window.BrowserSupport.runAllChecks();
    window.BrowserSupport.addFallbacks();
    window.BrowserSupport.showWarnings();
  }
})();
