/* ثيمات مختلفة للبرنامج */

/* الثيم الافتراضي */
[data-theme="default"] {
  --primary-color: #3498db;
  --secondary-color: #2ecc71;
  --accent-color: #e74c3c;
  --background-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --text-color: #2c3e50;
  --card-bg: rgba(255, 255, 255, 0.95);
}

/* الثيم المحيطي */
[data-theme="ocean"] {
  --primary-color: #1abc9c;
  --secondary-color: #3498db;
  --accent-color: #e67e22;
  --background-gradient: linear-gradient(135deg, #89f7fe 0%, #66a6ff 100%);
  --text-color: #2c3e50;
  --card-bg: rgba(255, 255, 255, 0.95);
}

/* ثيم الغروب */
[data-theme="sunset"] {
  --primary-color: #e74c3c;
  --secondary-color: #f39c12;
  --accent-color: #9b59b6;
  --background-gradient: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  --text-color: #2c3e50;
  --card-bg: rgba(255, 255, 255, 0.95);
}

/* ثيم الغابة */
[data-theme="forest"] {
  --primary-color: #27ae60;
  --secondary-color: #2ecc71;
  --accent-color: #f39c12;
  --background-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  --text-color: #2c3e50;
  --card-bg: rgba(255, 255, 255, 0.95);
}

/* الثيم الليلي */
[data-theme="dark"] {
  --primary-color: #5dade2;
  --secondary-color: #58d68d;
  --accent-color: #ec7063;
  --background-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  --text-color: #ecf0f1;
  --card-bg: rgba(52, 73, 94, 0.95);
}

/* الثيم الكلاسيكي */
[data-theme="classic"] {
  --primary-color: #8e44ad;
  --secondary-color: #9b59b6;
  --accent-color: #e67e22;
  --background-gradient: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  --text-color: #2c3e50;
  --card-bg: rgba(255, 255, 255, 0.98);
}

/* ثيم الربيع */
[data-theme="spring"] {
  --primary-color: #f39c12;
  --secondary-color: #e67e22;
  --accent-color: #e74c3c;
  --background-gradient: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
  --text-color: #2d3436;
  --card-bg: rgba(255, 255, 255, 0.95);
}

/* ثيم الشتاء */
[data-theme="winter"] {
  --primary-color: #74b9ff;
  --secondary-color: #0984e3;
  --accent-color: #fd79a8;
  --background-gradient: linear-gradient(135deg, #ddd6fe 0%, #e0e7ff 100%);
  --text-color: #2d3436;
  --card-bg: rgba(255, 255, 255, 0.95);
}

/* تأثيرات الثيمات على العناصر المختلفة */

/* أزرار الثيمات */
.theme-preview {
  width: 100px;
  height: 60px;
  border-radius: 8px;
  margin: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.theme-preview:hover {
  transform: scale(1.1);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.theme-preview.active {
  border: 3px solid var(--primary-color);
}

.default-preview {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.ocean-preview {
  background: linear-gradient(135deg, #89f7fe 0%, #66a6ff 100%);
}

.sunset-preview {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

.forest-preview {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.dark-preview {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

.classic-preview {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.spring-preview {
  background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
}

.winter-preview {
  background: linear-gradient(135deg, #ddd6fe 0%, #e0e7ff 100%);
}

/* تخصيصات للثيم الليلي */
[data-theme="dark"] .main-nav {
  background: rgba(52, 73, 94, 0.95);
}

[data-theme="dark"] .nav-btn {
  color: #ecf0f1;
}

[data-theme="dark"] .nav-btn:hover {
  background: var(--primary-color);
}

[data-theme="dark"] .search-box input {
  background: #34495e;
  color: #ecf0f1;
  border: 1px solid #5d6d7e;
}

[data-theme="dark"] .form-group input,
[data-theme="dark"] .form-group textarea,
[data-theme="dark"] .form-group select {
  background: #34495e;
  color: #ecf0f1;
  border-color: #5d6d7e;
}

[data-theme="dark"] .report-table {
  background: #34495e;
  color: #ecf0f1;
}

[data-theme="dark"] .report-table th {
  background: var(--primary-color);
}

[data-theme="dark"] .report-table tr:hover {
  background: #5d6d7e;
}

/* تأثيرات الانتقال بين الثيمات */
.theme-transition {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

body.theme-transition,
body.theme-transition *,
body.theme-transition *:before,
body.theme-transition *:after {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1) !important;
  transition-delay: 0 !important;
}

/* تخصيصات إضافية للثيمات */
.theme-selector-advanced {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
  margin: 2rem 0;
}

.theme-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  border-radius: var(--border-radius);
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: var(--transition);
}

.theme-option:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.theme-option.selected {
  border: 2px solid var(--primary-color);
}

.theme-name {
  margin-top: 0.5rem;
  font-weight: 600;
  color: var(--text-color);
}

/* تأثيرات خاصة للثيمات */
.gradient-text {
  background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.theme-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background: var(--accent-color);
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: bold;
}

/* تحسينات للطباعة حسب الثيم */
@media print {
  [data-theme="dark"] {
    --text-color: #000 !important;
    --card-bg: #fff !important;
  }

  [data-theme="dark"] .page-container {
    background: white !important;
    color: black !important;
  }
}

/* تأثيرات الحركة للثيمات */
.theme-animation {
  animation: themeChange 0.6s ease-in-out;
}

@keyframes themeChange {
  0% {
    opacity: 0.8;
    transform: scale(0.98);
  }
  50% {
    opacity: 0.9;
    transform: scale(1.02);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* تخصيصات خاصة لكل ثيم */
/* تخصيصات خاصة لكل ثيم */

/* ثيم الذهبي */
[data-theme="golden"] {
  --primary-color: #f39c12;
  --secondary-color: #e67e22;
  --accent-color: #d35400;
  --background-gradient: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
  --text-color: #2c3e50;
  --card-bg: rgba(255, 255, 255, 0.95);
}

/* ثيم البنفسجي */
[data-theme="purple"] {
  --primary-color: #9b59b6;
  --secondary-color: #8e44ad;
  --accent-color: #e74c3c;
  --background-gradient: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);
  --text-color: #2c3e50;
  --card-bg: rgba(255, 255, 255, 0.95);
}

/* متغيرات CSS للثيمات المتقدمة */
:root {
  --theme-transition-duration: 0.5s;
  --theme-transition-timing: cubic-bezier(0.4, 0, 0.2, 1);
}

/* مؤشر الثيم النشط */
.active-theme-indicator {
  position: fixed;
  bottom: 20px;
  left: 20px;
  background: var(--primary-color);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius);
  font-size: 0.875rem;
  z-index: 1000;
  opacity: 0;
  transform: translateY(20px);
  transition: var(--transition);
}

.active-theme-indicator.show {
  opacity: 1;
  transform: translateY(0);
}
