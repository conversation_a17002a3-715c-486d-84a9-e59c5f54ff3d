// مفاتيح التخزين
const STORAGE_KEYS = {
  RECORDS: 'archiveRecords',
  THEME: 'selectedTheme',
  SETTINGS: 'appSettings'
};

// حفظ السجلات في التخزين المحلي
function saveRecordsToStorage() {
  try {
    localStorage.setItem(STORAGE_KEYS.RECORDS, JSON.stringify(allRecords));
    return true;
  } catch (error) {
    console.error('خطأ في حفظ البيانات:', error);
    showMessage('حدث خطأ في حفظ البيانات', 'error');
    return false;
  }
}

// تحميل السجلات من التخزين المحلي
function loadRecordsFromStorage() {
  try {
    const stored = localStorage.getItem(STORAGE_KEYS.RECORDS);
    if (stored) {
      allRecords = JSON.parse(stored);
      // التحقق من صحة البيانات المحملة
      allRecords = allRecords.filter(record => record && record.id);
    } else {
      allRecords = [];
    }
    return true;
  } catch (error) {
    console.error('خطأ في تحميل البيانات:', error);
    allRecords = [];
    showMessage('حدث خطأ في تحميل البيانات', 'error');
    return false;
  }
}

// حفظ الثيم المحدد
function saveThemeToStorage(theme) {
  try {
    localStorage.setItem(STORAGE_KEYS.THEME, theme);
    return true;
  } catch (error) {
    console.error('خطأ في حفظ الثيم:', error);
    return false;
  }
}

// تحميل الثيم المحفوظ
function loadThemeFromStorage() {
  try {
    return localStorage.getItem(STORAGE_KEYS.THEME) || 'blue';
  } catch (error) {
    console.error('خطأ في تحميل الثيم:', error);
    return 'blue';
  }
}

// حفظ الإعدادات العامة
function saveSettingsToStorage(settings) {
  try {
    localStorage.setItem(STORAGE_KEYS.SETTINGS, JSON.stringify(settings));
    return true;
  } catch (error) {
    console.error('خطأ في حفظ الإعدادات:', error);
    return false;
  }
}

// تحميل الإعدادات العامة
function loadSettingsFromStorage() {
  try {
    const stored = localStorage.getItem(STORAGE_KEYS.SETTINGS);
    return stored ? JSON.parse(stored) : getDefaultSettings();
  } catch (error) {
    console.error('خطأ في تحميل الإعدادات:', error);
    return getDefaultSettings();
  }
}

// الإعدادات الافتراضية
function getDefaultSettings() {
  return {
    theme: 'blue',
    dateFormat: 'dd/mm/yyyy',
    recordsPerPage: 10,
    autoSave: true,
    backupFrequency: 'weekly'
  };
}

// إنشاء نسخة احتياطية
function createBackup() {
  const backupData = {
    records: allRecords,
    settings: loadSettingsFromStorage(),
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  };
  
  try {
    const backupStr = JSON.stringify(backupData, null, 2);
    const backupBlob = new Blob([backupStr], {type: 'application/json'});
    
    const link = document.createElement('a');
    link.href = URL.createObjectURL(backupBlob);
    link.download = `archive_backup_${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    
    showMessage('تم إنشاء النسخة الاحتياطية بنجاح', 'success');
    return true;
  } catch (error) {
    console.error('خطأ في إنشاء النسخة الاحتياطية:', error);
    showMessage('حدث خطأ في إنشاء النسخة الاحتياطية', 'error');
    return false;
  }
}

// استعادة من النسخة الاحتياطية
function restoreFromBackup(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    
    reader.onload = function(e) {
      try {
        const backupData = JSON.parse(e.target.result);
        
        // التحقق من صحة النسخة الاحتياطية
        if (!backupData.records || !Array.isArray(backupData.records)) {
          throw new Error('تنسيق النسخة الاحتياطية غير صحيح');
        }
        
        // استعادة البيانات
        allRecords = backupData.records;
        saveRecordsToStorage();
        
        // استعادة الإعدادات
        if (backupData.settings) {
          saveSettingsToStorage(backupData.settings);
        }
        
        // تحديث الواجهات
        updateReportTable();
        updateRecordDisplay();
        populateDepartmentFilter();
        
        showMessage(`تم استعادة ${backupData.records.length} سجل بنجاح`, 'success');
        resolve(backupData);
        
      } catch (error) {
        console.error('خطأ في استعادة النسخة الاحتياطية:', error);
        showMessage('حدث خطأ في قراءة النسخة الاحتياطية', 'error');
        reject(error);
      }
    };
    
    reader.onerror = function() {
      reject(new Error('فشل في قراءة الملف'));
    };
    
    reader.readAsText(file);
  });
}

// فحص سعة التخزين المتاحة
function checkStorageQuota() {
  if ('storage' in navigator && 'estimate' in navigator.storage) {
    navigator.storage.estimate().then(estimate => {
      const used = estimate.usage || 0;
      const quota = estimate.quota || 0;
      const percentage = ((used / quota) * 100).toFixed(2);
      
      console.log(`استخدام التخزين: ${used} بايت من ${quota} بايت (${percentage}%)`);
      
      if (percentage > 80) {
        showMessage('تحذير: مساحة التخزين المحلي ممتلئة تقريباً', 'warning');
      }
    });
  }
}

// مسح بيانات التخزين المحلي
function clearAllStorage() {
  try {
    Object.values(STORAGE_KEYS).forEach(key => {
      localStorage.removeItem(key);
    });
    
    // إعادة تعيين المتغيرات
    allRecords = [];
    filteredRecords = [];
    currentRecordIndex = 0;
    
    showMessage('تم مسح جميع البيانات المحفوظة', 'success');return true;
  } catch (error) {
    console.error('خطأ في مسح البيانات:', error);
    showMessage('حدث خطأ في مسح البيانات', 'error');
    return false;
  }
}

// تصدير البيانات بتنسيقات مختلفة
function exportToCSV() {
  if (allRecords.length === 0) {
    showMessage('لا توجد بيانات للتصدير', 'error');
    return;
  }
  
  const headers = ['رقم الكتاب', 'التاريخ', 'الموضوع', 'الفحوى/الموظف', 'الدائرة المعنية', 'الهامش'];
  const csvContent = [
    headers.join(','),
    ...allRecords.map(record => [
      record.bookNumber,
      `${record.day}/${record.month}/${record.year}`,
      record.subject,
      record.content,
      record.department,
      record.notes || ''
    ].map(field => `"${field}"`).join(','))
  ].join('\n');
  
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.download = `archive_data_${new Date().toISOString().split('T')[0]}.csv`;
  link.click();
  
  showMessage('تم تصدير البيانات بصيغة CSV', 'success');
}

// فحص التخزين المحلي دورياً
setInterval(checkStorageQuota, 60000); // كل دقيقة