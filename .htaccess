# ملف .htaccess لدعم جميع الخوادم والمتصفحات

# تفعيل إعادة الكتابة
RewriteEngine On

# دعم HTTPS
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# ضغط الملفات لتحسين الأداء
<IfModule mod_deflate.c>
    # ضغط النصوص
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
    AddOutputFilterByType DEFLATE application/manifest+json
</IfModule>

# تحديد أنواع الملفات
<IfModule mod_mime.c>
    # JavaScript
    AddType application/javascript .js
    AddType application/json .json
    
    # CSS
    AddType text/css .css
    
    # HTML
    AddType text/html .html .htm
    
    # الخطوط
    AddType application/font-woff .woff
    AddType application/font-woff2 .woff2
    AddType application/vnd.ms-fontobject .eot
    AddType application/x-font-ttf .ttf
    AddType image/svg+xml .svg
    
    # الصور
    AddType image/webp .webp
    AddType image/avif .avif
    
    # PWA
    AddType application/manifest+json .webmanifest
    AddType text/cache-manifest .appcache
</IfModule>

# تحسين التخزين المؤقت
<IfModule mod_expires.c>
    ExpiresActive On
    
    # الصور
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    
    # الخطوط
    ExpiresByType application/font-woff "access plus 1 year"
    ExpiresByType application/font-woff2 "access plus 1 year"
    ExpiresByType application/vnd.ms-fontobject "access plus 1 year"
    ExpiresByType application/x-font-ttf "access plus 1 year"
    
    # CSS و JavaScript
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    
    # HTML
    ExpiresByType text/html "access plus 1 hour"
    
    # JSON و Manifest
    ExpiresByType application/json "access plus 1 day"
    ExpiresByType application/manifest+json "access plus 1 week"
</IfModule>

# رؤوس التخزين المؤقت
<IfModule mod_headers.c>
    # تحسين الأمان
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    
    # دعم PWA
    Header set Cache-Control "public, max-age=31536000" "expr=%{REQUEST_URI} =~ m#\.(ico|css|js|gif|jpe?g|png|svg|woff2?|ttf|eot)$#"
    Header set Cache-Control "public, max-age=3600" "expr=%{REQUEST_URI} =~ m#\.(html|htm)$#"
    
    # دعم Service Worker
    <FilesMatch "sw\.js$">
        Header set Cache-Control "no-cache, no-store, must-revalidate"
        Header set Pragma "no-cache"
        Header set Expires 0
    </FilesMatch>
    
    # دعم Manifest
    <FilesMatch "manifest\.json$">
        Header set Cache-Control "public, max-age=604800"
    </FilesMatch>
</IfModule>

# منع الوصول للملفات الحساسة
<Files ".htaccess">
    Order allow,deny
    Deny from all
</Files>

<Files "*.log">
    Order allow,deny
    Deny from all
</Files>

<Files "*.bak">
    Order allow,deny
    Deny from all
</Files>

# دعم UTF-8
AddDefaultCharset UTF-8

# تحسين الأداء
<IfModule mod_pagespeed.c>
    ModPagespeed on
    ModPagespeedEnableFilters rewrite_css,rewrite_javascript,rewrite_images
    ModPagespeedEnableFilters collapse_whitespace,remove_comments
</IfModule>

# دعم CORS للخطوط
<IfModule mod_headers.c>
    <FilesMatch "\.(ttf|ttc|otf|eot|woff|woff2|font.css)$">
        Header set Access-Control-Allow-Origin "*"
    </FilesMatch>
</IfModule>

# إعادة توجيه للصفحة الرئيسية
DirectoryIndex index.html index.htm

# معالجة الأخطاء
ErrorDocument 404 /index.html
ErrorDocument 403 /index.html
ErrorDocument 500 /index.html

# تحسين الأمان
ServerTokens Prod
ServerSignature Off

# منع hotlinking للصور
RewriteCond %{HTTP_REFERER} !^$
RewriteCond %{HTTP_REFERER} !^http(s)?://(www\.)?yourdomain.com [NC]
RewriteRule \.(jpg|jpeg|png|gif|webp)$ - [NC,F,L]

# تحسين السرعة
<IfModule mod_rewrite.c>
    # إزالة www (اختياري)
    RewriteCond %{HTTP_HOST} ^www\.(.*)$ [NC]
    RewriteRule ^(.*)$ http://%1/$1 [R=301,L]
    
    # إزالة .html من الروابط
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^([^\.]+)$ $1.html [NC,L]
</IfModule>

# دعم البيانات المضغوطة
<IfModule mod_gzip.c>
    mod_gzip_on Yes
    mod_gzip_dechunk Yes
    mod_gzip_item_include file \.(html?|txt|css|js|php|pl)$
    mod_gzip_item_include mime ^application/x-javascript.*
    mod_gzip_item_include mime ^text/.*
    mod_gzip_item_exclude rspheader ^Content-Encoding:.*gzip.*
    mod_gzip_item_exclude mime ^image/.*
    mod_gzip_item_include handler ^cgi-script$
</IfModule>
