# دليل التثبيت والتشغيل - برنامج الأرشفة الإلكترونية

## 🌐 التشغيل على جميع المتصفحات والأنظمة

### الطريقة الأولى: التشغيل المباشر من المتصفح

#### 1. تحميل الملفات
- قم بتحميل جميع ملفات المشروع
- تأكد من وجود جميع المجلدات والملفات

#### 2. فتح البرنامج
- افتح ملف `index.html` في أي متصفح
- أو اضغط مرتين على الملف لفتحه تلقائياً

#### 3. المتصفحات المدعومة
- **Chrome** 30+ ✅
- **Firefox** 25+ ✅
- **Safari** 7+ ✅
- **Edge** جميع الإصدارات ✅
- **Internet Explorer** 9+ ⚠️ (مع قيود)
- **Opera** 17+ ✅

---

### الطريقة الثانية: تثبيت كتطبيق PWA

#### على الكمبيوتر:
1. افتح البرنامج في Chrome أو Edge
2. ابحث عن أيقونة "تثبيت" في شريط العنوان
3. اضغط على "تثبيت التطبيق"
4. سيظهر التطبيق في قائمة البرامج

#### على الهاتف/الجهاز اللوحي:
1. افتح البرنامج في المتصفح
2. اضغط على قائمة المتصفح (⋮)
3. اختر "إضافة إلى الشاشة الرئيسية"
4. سيظهر التطبيق كأيقونة على الشاشة

---

### الطريقة الثالثة: تشغيل على خادم محلي

#### باستخدام Python:
```bash
# Python 3
python -m http.server 8000

# Python 2
python -m SimpleHTTPServer 8000
```

#### باستخدام Node.js:
```bash
# تثبيت http-server
npm install -g http-server

# تشغيل الخادم
http-server -p 8000
```

#### باستخدام PHP:
```bash
php -S localhost:8000
```

ثم افتح المتصفح واذهب إلى: `http://localhost:8000`

---

## 🖥️ التشغيل على أنظمة التشغيل المختلفة

### Windows (جميع الإصدارات)
1. **Windows XP/Vista/7/8/10/11**
2. افتح مستكشف الملفات
3. انتقل إلى مجلد البرنامج
4. اضغط مرتين على `index.html`
5. سيفتح في المتصفح الافتراضي

### macOS (جميع الإصدارات)
1. **macOS 10.9+ (Mavericks وأحدث)**
2. افتح Finder
3. انتقل إلى مجلد البرنامج
4. اضغط مرتين على `index.html`
5. سيفتح في Safari أو المتصفح الافتراضي

### Linux (جميع التوزيعات)
1. **Ubuntu/Debian/CentOS/Fedora/openSUSE**
2. افتح مدير الملفات
3. انتقل إلى مجلد البرنامج
4. اضغط بالزر الأيمن على `index.html`
5. اختر "فتح باستخدام المتصفح"

### Android
1. **Android 4.4+ (KitKat وأحدث)**
2. انسخ الملفات إلى التخزين الداخلي
3. افتح مدير الملفات
4. انتقل إلى مجلد البرنامج
5. اضغط على `index.html`
6. اختر المتصفح لفتح الملف

### iOS (iPhone/iPad)
1. **iOS 7+ وأحدث**
2. استخدم تطبيق Files
3. انسخ الملفات إلى iCloud Drive
4. اضغط على `index.html`
5. سيفتح في Safari

---

## 🔧 حل المشاكل الشائعة

### المشكلة: البرنامج لا يعمل في Internet Explorer
**الحل:**
- تأكد من استخدام IE 9 أو أحدث
- فعّل JavaScript في إعدادات المتصفح
- أضف الموقع إلى المواقع الموثوقة

### المشكلة: لا يتم حفظ البيانات
**الحل:**
- تأكد من تفعيل localStorage في المتصفح
- تحقق من إعدادات الخصوصية
- جرب تشغيل البرنامج من خادم محلي

### المشكلة: التصميم يظهر بشكل خاطئ
**الحل:**
- تأكد من تحميل جميع ملفات CSS
- امسح ذاكرة التخزين المؤقت للمتصفح
- تأكد من دعم المتصفح لـ CSS3

### المشكلة: البرنامج بطيء على الجهاز
**الحل:**
- سيتم تفعيل وضع الأداء المنخفض تلقائياً
- أغلق التطبيقات الأخرى
- استخدم متصفح أحدث

---

## ⚙️ الإعدادات المتقدمة

### تخصيص المسار:
يمكنك وضع البرنامج في أي مجلد وسيعمل بشكل طبيعي

### تخصيص الخادم:
لتشغيل البرنامج على شبكة محلية، استخدم:
```bash
http-server -p 8000 -a 0.0.0.0
```

### تخصيص الأمان:
لتشغيل البرنامج بـ HTTPS:
```bash
http-server -p 8000 -S -C cert.pem -K key.pem
```

---

## 📱 التثبيت على الأجهزة المحمولة

### Android:
1. افتح Chrome أو Firefox
2. اذهب إلى البرنامج
3. اضغط على "إضافة إلى الشاشة الرئيسية"
4. سيعمل كتطبيق مستقل

### iOS:
1. افتح Safari
2. اذهب إلى البرنامج
3. اضغط على زر المشاركة
4. اختر "إضافة إلى الشاشة الرئيسية"

---

## 🔄 التحديثات

### التحديث التلقائي:
- البرنامج يفحص التحديثات تلقائياً
- ستظهر رسالة عند توفر تحديث جديد
- اضغط "موافق" لتحديث البرنامج

### التحديث اليدوي:
1. حمّل النسخة الجديدة
2. استبدل الملفات القديمة
3. البيانات ستبقى محفوظة تلقائياً

---

## 📞 الدعم الفني

### في حالة وجود مشاكل:
1. تأكد من تحديث المتصفح
2. امسح ذاكرة التخزين المؤقت
3. جرب متصفح آخر
4. تأكد من تفعيل JavaScript

### معلومات النظام:
يمكنك الضغط على F12 في المتصفح لفتح أدوات المطور ومراجعة أي أخطاء في وحدة التحكم (Console).

---

## ✅ التحقق من التثبيت

بعد فتح البرنامج، تأكد من:
- ظهور الصفحة الرئيسية بشكل صحيح
- عمل جميع الأزرار والقوائم
- إمكانية إضافة سجل تجريبي
- حفظ البيانات بعد إعادة تحميل الصفحة

إذا كان كل شيء يعمل بشكل صحيح، فقد تم التثبيت بنجاح! 🎉
