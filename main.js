const { app, BrowserWindow, ipcMain, dialog } = require('electron');
const path = require('path');
const fs = require('fs');

// تحديد مسار حفظ البيانات
const userDataPath = app.getPath('userData');
const dataFilePath = path.join(userDataPath, 'archive-data.json');

// إنشاء النافذة الرئيسية
function createWindow() {
  const mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(__dirname, 'assets/icon.png'),
    title: 'برنامج الأرشفة الإلكترونية'
  });

  // تحميل الصفحة الرئيسية
  mainWindow.loadFile('index.html');

  // إزالة القائمة العلوية
  mainWindow.setMenu(null);

  // فتح أدوات المطور في وضع التطوير
  if (process.env.NODE_ENV === 'development') {
    mainWindow.webContents.openDevTools();
  }
}

// عند جاهزية التطبيق
app.whenReady().then(() => {
  createWindow();

  app.on('activate', function () {
    if (BrowserWindow.getAllWindows().length === 0) createWindow();
  });
});

// إغلاق التطبيق عند إغلاق جميع النوافذ (ماعدا macOS)
app.on('window-all-closed', function () {
  if (process.platform !== 'darwin') app.quit();
});

// استقبال طلبات حفظ البيانات
ipcMain.on('save-data', (event, data) => {
  try {
    fs.writeFileSync(dataFilePath, JSON.stringify(data, null, 2), 'utf-8');
    event.reply('save-data-reply', { success: true });
  } catch (error) {
    console.error('خطأ في حفظ البيانات:', error);
    event.reply('save-data-reply', { success: false, error: error.message });
  }
});

// استقبال طلبات قراءة البيانات
ipcMain.on('load-data', (event) => {
  try {
    if (fs.existsSync(dataFilePath)) {
      const data = fs.readFileSync(dataFilePath, 'utf-8');
      event.reply('load-data-reply', { success: true, data: JSON.parse(data) });
    } else {
      event.reply('load-data-reply', { success: true, data: [] });
    }
  } catch (error) {
    console.error('خطأ في قراءة البيانات:', error);
    event.reply('load-data-reply', { success: false, error: error.message });
  }
});

// استقبال طلبات تصدير البيانات
ipcMain.on('export-data', async (event, data) => {
  try {
    const { filePath } = await dialog.showSaveDialog({
      title: 'تصدير البيانات',
      defaultPath: path.join(app.getPath('documents'), `archive-backup-${new Date().toISOString().split('T')[0]}.json`),
      filters: [
        { name: 'ملفات JSON', extensions: ['json'] }
      ]
    });

    if (filePath) {
      fs.writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf-8');
      event.reply('export-data-reply', { success: true });
    } else {
      event.reply('export-data-reply', { success: false, error: 'تم إلغاء التصدير' });
    }
  } catch (error) {
    console.error('خطأ في تصدير البيانات:', error);
    event.reply('export-data-reply', { success: false, error: error.message });
  }
});

// استقبال طلبات استيراد البيانات
ipcMain.on('import-data', async (event) => {
  try {
    const { filePaths } = await dialog.showOpenDialog({
      title: 'استيراد البيانات',
      filters: [
        { name: 'ملفات JSON', extensions: ['json'] }
      ],
      properties: ['openFile']
    });

    if (filePaths && filePaths.length > 0) {
      const data = fs.readFileSync(filePaths[0], 'utf-8');
      event.reply('import-data-reply', { success: true, data: JSON.parse(data) });
    } else {
      event.reply('import-data-reply', { success: false, error: 'تم إلغاء الاستيراد' });
    }
  } catch (error) {
    console.error('خطأ في استيراد البيانات:', error);
    event.reply('import-data-reply', { success: false, error: error.message });
  }
});