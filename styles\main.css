/* متغيرات CSS مع دعم المتصفحات القديمة */
:root {
  --primary-color: #87ceeb;
  --secondary-color: #4169e1;
  --accent-color: #87ceeb;
  --background-gradient: linear-gradient(135deg, #87ceeb 0%, #4169e1 100%);
  --text-color: #4169e1;
  --card-bg: rgba(255, 255, 255, 0.95);
  --border-radius: 12px;
  --shadow: 0 8px 32px rgba(135, 206, 235, 0.3);
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* دعم المتصفحات القديمة التي لا تدعم CSS Variables */
.fallback-primary { color: #87ceeb; }
.fallback-secondary { color: #4169e1; }
.fallback-accent { color: #87ceeb; }
.fallback-text { color: #4169e1; }

/* إعادة تعيين شاملة مع دعم جميع المتصفحات */
* {
  margin: 0;
  padding: 0;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

/* دعم المتصفحات القديمة */
*,
*:before,
*:after {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

body {
  /* خطوط متوافقة مع جميع الأنظمة */
  font-family: 'Segoe UI', 'Tahoma', 'Geneva', 'Verdana', 'Arial', 'Helvetica', 'DejaVu Sans', 'Liberation Sans', 'Noto Sans Arabic', 'Arabic Typesetting', sans-serif;

  /* خلفية متدرجة مع دعم المتصفحات القديمة */
  background: #87ceeb; /* fallback */
  background: -webkit-linear-gradient(135deg, #87ceeb 0%, #4169e1 100%);
  background: -moz-linear-gradient(135deg, #87ceeb 0%, #4169e1 100%);
  background: -o-linear-gradient(135deg, #87ceeb 0%, #4169e1 100%);
  background: linear-gradient(135deg, #87ceeb 0%, #4169e1 100%);
  background: var(--background-gradient, linear-gradient(135deg, #87ceeb 0%, #4169e1 100%));

  color: #4169e1; /* fallback */
  color: var(--text-color, #4169e1);

  min-height: 100vh;
  min-height: -webkit-fill-available; /* iOS Safari */
  line-height: 1.6;
  direction: rtl;

  /* إضافة padding-top لتجنب تداخل المحتوى مع شريط التنقل */
  padding-top: 80px;
  margin: 0;
  padding-left: 0;
  padding-right: 0;
  padding-bottom: 0;

  /* تحسينات للأداء */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;

  /* منع التمرير الأفقي */
  overflow-x: hidden;

  /* دعم الشاشات عالية الدقة */
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
}

/* شريط التنقل */
.main-nav {
  background: rgba(135, 206, 235, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 10px rgba(135, 206, 235, 0.3);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  height: 80px;
  margin-bottom: 0;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  padding: 1rem 2rem;
  gap: 2rem;
}

.nav-brand h1 {
  color: var(--primary-color);
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
}

.developer-name {
  margin: 0.25rem 0 0 0;
  font-size: 0.8rem;
  color: #ecf0f1;
  opacity: 0.9;
  font-weight: normal;
}

.nav-menu {
  display: flex;
  gap: 0.5rem;
  flex: 1;
}

.nav-btn {
  background: transparent;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-radius);
  color: white;
  cursor: pointer;
  transition: var(--transition);
  font-weight: 500;
}

.nav-btn:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-2px);
}

.nav-btn.active {
  background: var(--primary-color);
  color: white;
}

.nav-tools {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.search-box {
  display: flex;
  background: white;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(135, 206, 235, 0.3);
}

.search-box input {
  border: none;
  padding: 0.5rem 1rem;
  outline: none;
  width: 200px;
}

.search-box button {
  background: var(--primary-color);
  border: none;
  padding: 0.5rem 1rem;
  color: white;
  cursor: pointer;
}

.tool-btn {
  padding: 0.5rem !important;
  min-width: 40px;
}

/* تخطيط الصفحات */
.page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  opacity: 1;
  transform: translateY(0);
  transition: var(--transition);
  min-height: calc(100vh - 80px);
}

.page.fade-in {
  opacity: 1;
  transform: translateY(0);
}

.page-container {
  background: var(--card-bg);
  border-radius: var(--border-radius);
  padding: 2rem;
  box-shadow: var(--shadow);
  backdrop-filter: blur(10px);
}

.page-header {
  text-align: center;
  margin-bottom: 2rem;
}

.page-header h2 {
  color: var(--primary-color);
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.page-header p {
  color: #666;
  font-size: 1.1rem;
}

/* الصفحة الرئيسية */
.welcome-container {
  text-align: center;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(135, 206, 235, 0.3);
}

.welcome-header {
  margin-bottom: 3rem;
  background: rgba(255, 255, 255, 0.9);
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(135, 206, 235, 0.3);
}

.welcome-header h2 {
  color: var(--primary-color);
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.welcome-header p {
  font-size: 1.2rem;
  color: #666;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.feature-card {
  background: rgba(255, 255, 255, 0.95);
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(135, 206, 235, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  border: 2px solid transparent;
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 12px 40px rgba(135, 206, 235, 0.4);
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.feature-card h3 {
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.quick-stats {
  display: flex;
  justify-content: center;
  gap: 3rem;
  margin-top: 2rem;
  background: rgba(255, 255, 255, 0.9);
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(135, 206, 235, 0.3);
}

.stat-item {
  text-align: center;
  background: rgba(135, 206, 235, 0.1);
  padding: 1.5rem;
  border-radius: 8px;
  border: 2px solid rgba(135, 206, 235, 0.3);
}

.stat-number {
  display: block;
  font-size: 2.5rem;
  font-weight: 700;
  color: #87ceeb;
  margin-bottom: 0.5rem;
}

.stat-label {
  color: #4169e1;
  font-size: 1rem;
  font-weight: 600;
}

/* النماذج */
.data-form, .filter-form {
  background: white;
  padding: 2rem;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--text-color);
}

.form-group input,
.form-group textarea,
.form-group select {
  padding: 0.75rem;
  border: 2px solid #e0e0e0;
  border-radius: var(--border-radius);
  font-size: 1rem;
  transition: var(--transition);
  background: white;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.form-group input.error {
  border-color: var(--accent-color);
}

.field-error {
  color: var(--accent-color);
  font-size: 0.875rem;
  margin-top: 0.5rem;
}

.date-inputs {
  display: flex;
  gap: 0.5rem;
}

.date-inputs input {
  flex: 1;
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
}

/* الأزرار */
.btn {
  padding: 0.75rem 2rem;
  border: none;
  border-radius: var(--border-radius);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.btn-primary {
  background: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background: #2980b9;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(52, 152, 219, 0.3);
}

.btn-secondary {
  background: #95a5a6;
  color: white;
}

.btn-secondary:hover {
  background: #7f8c8d;
}

.btn-success {
  background: var(--secondary-color);
  color: white;
}

.btn-success:hover {
  background: #27ae60;
}

.btn-warning {
  background: #f39c12;
  color: white;
}

.btn-warning:hover {
  background: #e67e22;
}

.btn-danger {
  background: var(--accent-color);
  color: white;
}

.btn-danger:hover {
  background: #c0392b;
}

.btn-info {
  background: #3498db;
  color: white;
}

.btn-info:hover {
  background: #2980b9;
}

/* الأرشيف */
.archive-controls {
  display: flex;
  gap: 1rem;
}

.archive-navigation {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin: 2rem 0;
  padding: 1rem;
  background: white;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
}

.record-counter {
  font-weight: 600;
  color: var(--primary-color);
  margin: 0 1rem;
}

.record-display {
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.record-card {
  background: white;
  padding: 2rem;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  width: 100%;
  max-width: 800px;
}

.record-card h3 {
  color: var(--primary-color);
  margin-bottom: 1.5rem;
  text-align: center;
  font-size: 1.5rem;
}

.record-details {
  margin-bottom: 2rem;
}

.record-details p {
  margin-bottom: 1rem;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 8px;
  border-right: 4px solid var(--primary-color);
}

.record-details strong {
  color: var(--primary-color);
  margin-left: 0.5rem;
}

.record-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.no-records {
  text-align: center;
  color: #666;
  font-size: 1.2rem;
}

/* التصفية */
.filter-section {
  background: white;
  padding: 1.5rem;
  border-radius: var(--border-radius);
  margin-bottom: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.filter-section h3 {
  color: var(--primary-color);
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #eee;
}

.date-range {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.filter-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

/* التقارير */
.report-actions {
  display: flex;
  gap: 1rem;
}

.report-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--shadow);
}

.report-table th,
.report-table td {
  padding: 1rem;
  text-align: right;
  border-bottom: 1px solid #eee;
}

.report-table th {
  background: var(--primary-color);
  color: white;
  font-weight: 600;
}

.report-table tr:hover {
  background: #f8f9fa;
}

/* الإعدادات */
.settings-section {
  background: white;
  padding: 2rem;
  border-radius: var(--border-radius);
  margin-bottom: 2rem;
  box-shadow: var(--shadow);
}

.settings-section h3 {
  color: var(--primary-color);
  margin-bottom: 1.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #eee;
}

.theme-selector {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.theme-btn {
  padding: 1rem 2rem;
  border: 3px solid transparent;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-weight: 600;
  min-width: 120px;
}

.default-theme {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.ocean-theme {
  background: linear-gradient(135deg, #1abc9c 0%, #3498db 100%);
  color: white;
}

.sunset-theme {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  color: #2c3e50;
}

.forest-theme {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  color: #2c3e50;
}

.theme-btn:hover {
  transform: scale(1.05);
  border-color: var(--primary-color);
}

.backup-options {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.info-item {
  display: flex;
  justify-content: space-between;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.info-item label {
  font-weight: 600;
  color: var(--primary-color);
}

/* الإشعارات */
.notification {
  position: fixed;
  top: 20px;
  left: 20px;
  background: white;
  padding: 1rem 1.5rem;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  transform: translateX(-100%);
  transition: var(--transition);
  z-index: 10000;
  max-width: 400px;
}

.notification.show {
  transform: translateX(0);
}

.notification-content {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.notification-icon {
  font-size: 1.5rem;
}

.notification-message {
  flex: 1;
}

.notification-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #666;
}

.notification-success {
  border-right: 4px solid var(--secondary-color);
}

.notification-error {
  border-right: 4px solid var(--accent-color);
}

.notification-warning {
  border-right: 4px solid #f39c12;
}

.notification-info {
  border-right: 4px solid var(--primary-color);
}

/* التلميحات */
.tooltip {
  position: absolute;
  background: #87ceeb;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-size: 0.875rem;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s;
  z-index: 10000;
}

.tooltip.show {
  opacity: 1;
}

.tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border: 5px solid transparent;
  border-top-color: #333;
}

/* الإحصائيات */
.statistics {
  background: white;
  padding: 2rem;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
}

.statistics h3 {
  color: var(--primary-color);
  text-align: center;
  margin-bottom: 2rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
}

.stat-item {
  text-align: center;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: var(--border-radius);
}

.stat-item h4 {
  font-size: 2.5rem;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
  .nav-container {
    flex-direction: column;
    gap: 1rem;
  }

  .nav-menu {
    order: 3;
    flex-wrap: wrap;
    justify-content: center;
  }

  .page {
    padding: 1rem;
  }

  .page-container {
    padding: 1rem;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .quick-stats {
    flex-direction: column;
    gap: 1rem;
  }

  .date-range {
    grid-template-columns: 1fr;
  }

  .archive-navigation {
    flex-wrap: wrap;
  }

  .theme-selector {
    justify-content: center;
  }

  .backup-options {
    justify-content: center;
  }

  .form-actions {
    flex-direction: column;
  }

  .report-actions {
    flex-direction: column;
  }

  .search-box input {
    width: 150px;
  }
}

@media (max-width: 480px) {
  .nav-brand h1 {
    font-size: 1.2rem;
  }

  .welcome-header h2 {
    font-size: 2rem;
  }

  .page-header h2 {
    font-size: 1.5rem;
  }

  .btn {
    padding: 0.5rem 1rem;
  }
}

/* طباعة */
@media print {
  .main-nav,
  .archive-navigation,
  .record-actions,
  .form-actions,
  .report-actions,
  .page-header {
    display: none !important;
  }

  .page {
    box-shadow: none;
    background: white;
    margin: 0;
    padding: 1rem;
  }

  .page-container {
    box-shadow: none;
    background: white;
  }

  .report-table {
    box-shadow: none;
  }
}

/* التحسينات الجديدة */

/* تمييز الحقول غير الصحيحة */
.invalid-field {
  border-color: var(--accent-color) !important;
  box-shadow: 0 0 0 2px rgba(231, 76, 60, 0.2) !important;
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

/* النوافذ المنبثقة */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(135, 206, 235, 0.5);
  display: none;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  backdrop-filter: blur(5px);
}

.modal-content {
  background: white;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  max-width: 90vw;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  padding: 1.5rem;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--primary-color);
  color: white;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.25rem;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: white;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.modal-close:hover {
  background: rgba(255, 255, 255, 0.2);
}

.modal-body {
  padding: 1.5rem;
  overflow-y: auto;
  flex: 1;
}

.modal-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid #eee;
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  background: #f8f9fa;
}

/* سجل البحث */
.search-history-modal {
  width: 600px;
}

.search-history-list {
  max-height: 400px;
  overflow-y: auto;
}

.search-history-item {
  padding: 1rem;
  border-bottom: 1px solid #eee;
  cursor: pointer;
  transition: background-color 0.2s;
}

.search-history-item:hover {
  background: #f8f9fa;
}

.search-query {
  font-weight: bold;
  margin-bottom: 0.5rem;
  color: var(--primary-color);
}

.search-info {
  display: flex;
  justify-content: space-between;
  font-size: 0.9rem;
  color: #666;
}

/* سجل العمليات */
.activity-log-modal {
  width: 700px;
}

.activity-log-list {
  max-height: 500px;
  overflow-y: auto;
}

.activity-item {
  padding: 1rem;
  border-bottom: 1px solid #eee;
}

.activity-description {
  font-weight: bold;
  margin-bottom: 0.5rem;
}

.activity-info {
  display: flex;
  justify-content: space-between;
  font-size: 0.9rem;
  color: #666;
}

.activity-action {
  background: var(--primary-color);
  color: white;
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
}

/* الإحصائيات */
.statistics-modal {
  width: 900px;
}

.statistics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.stat-card {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: var(--border-radius);
  border: 1px solid #eee;
}

.stat-card h4 {
  color: var(--primary-color);
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--primary-color);
}

.statistics-grid .stat-item {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 0;
  border-bottom: 1px solid #eee;
  text-align: left;
  background: none;
}

.statistics-grid .stat-item:last-child {
  border-bottom: none;
}

.stat-label {
  font-weight: 500;
}

.stat-value {
  font-weight: bold;
  color: var(--primary-color);
}

.department-stats,
.yearly-stats {
  max-height: 300px;
  overflow-y: auto;
}

/* تحسينات إضافية للتوافق والاستجابة */

/* دعم الشاشات عالية الدقة */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  body {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

/* تحسينات للأجهزة اللوحية الكبيرة */
@media screen and (max-width: 1024px) and (min-width: 769px) {
  .page-container {
    padding: 1.5rem;
  }

  .features-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .statistics-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .modal-content {
    max-width: 95vw;
  }
}

/* تحسينات للهواتف الذكية */
@media screen and (max-width: 768px) {
  /* تحسينات إضافية للنوافذ المنبثقة */
  .modal-content {
    margin: 1rem;
    max-width: calc(100vw - 2rem);
    max-height: calc(100vh - 2rem);
  }

  .statistics-modal,
  .search-history-modal,
  .activity-log-modal {
    width: auto;
  }

  .modal-header {
    padding: 1rem;
  }

  .modal-body {
    padding: 1rem;
  }

  .modal-footer {
    padding: 1rem;
    flex-direction: column;
  }

  /* تحسينات للأزرار */
  .btn {
    padding: 0.75rem 1rem;
    font-size: 1rem;
    min-height: 44px; /* حد أدنى للمس */
  }

  /* تحسينات للحقول */
  input, select, textarea {
    padding: 0.75rem;
    font-size: 1rem;
    min-height: 44px;
  }

  /* تحسينات للتنقل */
  .archive-navigation {
    gap: 0.5rem;
  }

  .archive-navigation .nav-btn {
    flex: 1;
    min-width: 70px;
    padding: 0.5rem;
  }

  /* تحسينات للبحث */
  .search-box input {
    width: 100%;
    max-width: none;
  }

  /* تحسينات للإحصائيات */
  .statistics-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .stat-card {
    padding: 1rem;
  }

  /* تحسينات للجداول */
  .report-table {
    font-size: 0.9rem;
  }

  .report-table th,
  .report-table td {
    padding: 0.5rem;
  }
}

/* تحسينات للهواتف الصغيرة */
@media screen and (max-width: 480px) {
  .nav-brand h1 {
    font-size: 1.1rem;
  }

  .welcome-header h2 {
    font-size: 1.8rem;
  }

  .page-header h2 {
    font-size: 1.4rem;
  }

  .feature-card {
    padding: 1rem;
  }

  .feature-icon {
    font-size: 2rem;
  }

  .btn {
    padding: 0.6rem 0.8rem;
    font-size: 0.9rem;
  }

  .modal-header h3 {
    font-size: 1.1rem;
  }

  .notification {
    max-width: calc(100vw - 2rem);
    right: 1rem;
    left: 1rem;
  }
}

/* تحسينات للشاشات الصغيرة جداً */
@media screen and (max-width: 320px) {
  .page {
    padding: 0.5rem;
  }

  .page-container {
    padding: 0.5rem;
  }

  .form-grid {
    gap: 0.5rem;
  }

  .features-grid {
    gap: 0.5rem;
  }

  .btn {
    padding: 0.5rem;
    font-size: 0.8rem;
  }
}

/* تحسينات للطباعة المحسنة */
@media print {
  * {
    -webkit-print-color-adjust: exact !important;
    color-adjust: exact !important;
  }

  .page {
    box-shadow: none !important;
    background: white !important;
    margin: 0 !important;
    padding: 1rem !important;
  }

  .report-table {
    border-collapse: collapse !important;
  }

  .report-table th,
  .report-table td {
    border: 1px solid #87ceeb !important;
    padding: 0.5rem !important;
  }
}

/* دعم الوضع المظلم للنظام */
@media (prefers-color-scheme: dark) {
  :root {
    --text-color: #e0e0e0;
    --card-bg: rgba(135, 206, 235, 0.95);
    --background-gradient: linear-gradient(135deg, #87ceeb 0%, #4169e1 100%);
  }

  body {
    background: var(--background-gradient);
    color: var(--text-color);
  }

  .modal-content,
  .notification,
  .page {
    background: #87ceeb;
    color: #e0e0e0;
  }
}

/* تحسينات لإمكانية الوصول */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* دعم اتجاه النص للغات المختلفة */
[dir="ltr"] {
  text-align: left;
}

[dir="rtl"] {
  text-align: right;
}

/* تحسينات للمتصفحات القديمة */
.no-flexbox .nav-container {
  display: block;
}

.no-grid .form-grid {
  display: block;
}

.no-grid .form-group {
  margin-bottom: 1rem;
}

/* تحسينات للأجهزة الضعيفة */
.low-performance * {
  transition: none !important;
  animation: none !important;
  transform: none !important;
}

.low-performance .notification {
  box-shadow: none;
  border: 1px solid #ddd;
}

.low-performance .modal-content {
  box-shadow: none;
  border: 2px solid #333;
}

/* تحسينات للأجهزة اللمسية */
.touch-device .btn {
  min-height: 44px;
  min-width: 44px;
  padding: 0.75rem 1rem;
}

.touch-device input,
.touch-device select,
.touch-device textarea {
  min-height: 44px;
  font-size: 16px; /* منع التكبير في iOS */
}

.touch-device .nav-btn {
  padding: 1rem;
  margin: 0.25rem;
}

/* تحسينات للشاشات الصغيرة */
.small-screen .page-container {
  padding: 0.5rem;
}

.small-screen .modal-content {
  margin: 0.5rem;
  border-radius: 8px;
}

.small-screen .notification {
  right: 0.5rem;
  left: 0.5rem;
  max-width: none;
}

.small-screen .btn {
  font-size: 0.9rem;
  padding: 0.6rem 0.8rem;
}

/* تحسينات للأجهزة اللوحية */
.tablet-mode .features-grid {
  grid-template-columns: repeat(2, 1fr);
}

.tablet-mode .form-grid {
  grid-template-columns: repeat(2, 1fr);
}

.tablet-mode .statistics-grid {
  grid-template-columns: repeat(2, 1fr);
}

/* تحسينات للطباعة المحسنة */
.print-area {
  background: white !important;
  color: #4169e1 !important;
}

.no-print {
  display: none !important;
}

@media print {
  .print-area table {
    border-collapse: collapse !important;
    width: 100% !important;
  }

  .print-area th,
  .print-area td {
    border: 1px solid #87ceeb !important;
    padding: 8px !important;
    text-align: right !important;
  }

  .print-area th {
    background: #f0f0f0 !important;
    font-weight: bold !important;
  }
}

/* تحسينات للمتصفحات القديمة جداً */
.ie8 .page,
.ie9 .page {
  background: white;
  border: 1px solid #ccc;
  box-shadow: none;
}

.ie8 .btn,
.ie9 .btn {
  border: 1px solid #ccc;
  background: #f0f0f0;
  color: #333;
}

.ie8 .btn:hover,
.ie9 .btn:hover {
  background: #e0e0e0;
}

/* دعم RTL محسن */
[dir="rtl"] .modal-close {
  left: 1.5rem;
  right: auto;
}

[dir="ltr"] .modal-close {
  right: 1.5rem;
  left: auto;
}

/* تحسينات للشاشات عالية الدقة */
@media (-webkit-min-device-pixel-ratio: 3), (min-resolution: 288dpi) {
  .btn {
    border-width: 0.5px;
  }

  .modal-content {
    border-width: 0.5px;
  }
}

/* دعم الوضع الليلي المحسن */
@media (prefers-color-scheme: dark) {
  .btn {
    background: #87ceeb;
    color: #fff;
    border-color: #4169e1;
  }

  .btn:hover {
    background: #4169e1;
  }

  input, select, textarea {
    background: #87ceeb;
    color: #fff;
    border-color: #4169e1;
  }

  .report-table th {
    background: #87ceeb;
    color: #fff;
  }

  .report-table td {
    background: #87ceeb;
    color: #fff;
    border-color: #4169e1;
  }
}

/* تحسينات للحركة المقللة */
@media (prefers-reduced-motion: reduce) {
  .notification {
    transform: none !important;
    transition: opacity 0.1s !important;
  }

  .modal-content {
    animation: none !important;
  }

  .fade-in,
  .slide-in {
    animation: none !important;
  }
}

/* دعم التباين العالي */
@media (prefers-contrast: high) {
  .btn {
    border-width: 2px;
    font-weight: bold;
  }

  .modal-content {
    border-width: 3px;
    border-color: #87ceeb;
  }

  .notification {
    border-width: 2px;
    border-color: #87ceeb;
  }
}

/* تحسينات للشاشات الكبيرة جداً */
@media screen and (min-width: 1920px) {
  .page-container {
    max-width: 1600px;
    margin: 0 auto;
  }

  .modal-content {
    max-width: 1200px;
  }
}

/* تحسينات للشاشات فائقة العرض */
@media screen and (min-width: 2560px) {
  body {
    font-size: 18px;
  }

  .btn {
    padding: 1rem 1.5rem;
    font-size: 1.1rem;
  }

  input, select, textarea {
    padding: 1rem;
    font-size: 1.1rem;
  }
}

/* أنماط الإشعارات */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  max-width: 400px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateX(100%);
  transition: transform 0.3s ease;
  z-index: 10000;
  border-left: 4px solid #87ceeb;
}

.notification.show {
  transform: translateX(0);
}

.notification-content {
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.notification-icon {
  font-size: 1.2rem;
  flex-shrink: 0;
}

.notification-message {
  flex: 1;
  font-size: 0.9rem;
  line-height: 1.4;
  white-space: pre-line;
}

.notification-close {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.notification-close:hover {
  background-color: #f0f0f0;
}

/* أنواع الإشعارات */
.notification-success {
  border-left-color: #87ceeb;
  background: #f0f8ff;
}

.notification-error {
  border-left-color: #87ceeb;
  background: #f0f8ff;
}

.notification-warning {
  border-left-color: #87ceeb;
  background: #f0f8ff;
}

.notification-info {
  border-left-color: #87ceeb;
  background: #f0f8ff;
}

/* أنماط السجلات */
.record-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 1rem;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #ecf0f1;
}

.record-header h3 {
  margin: 0;
  color: #4169e1;
  font-size: 1.2rem;
}

.record-date {
  background: #87ceeb;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: bold;
}

.record-body {
  margin-bottom: 1.5rem;
}

.record-field {
  margin-bottom: 1rem;
}

.record-field label {
  display: block;
  font-weight: bold;
  color: #4169e1;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.record-field p {
  margin: 0;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 6px;
  border-right: 3px solid #87ceeb;
  line-height: 1.5;
}

.record-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
}

.record-actions .btn {
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
}

/* رسالة عدم وجود سجلات */
.no-records {
  text-align: center;
  padding: 3rem;
  color: #7f8c8d;
  font-size: 1.1rem;
  background: #f8f9fa;
  border-radius: 12px;
  border: 2px dashed #bdc3c7;
}

/* تحسينات أزرار التنقل */
.archive-navigation {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin: 1rem 0;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 12px;
}

.record-counter {
  font-weight: bold;
  color: #4169e1;
  padding: 0.5rem 1rem;
  background: white;
  border-radius: 20px;
  border: 2px solid #87ceeb;
  min-width: 120px;
  text-align: center;
}

/* تحسينات الجدول */
.report-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 1rem;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.report-table th {
  background: #87ceeb;
  color: white;
  padding: 1rem;
  text-align: right;
  font-weight: bold;
  border-bottom: 2px solid #4169e1;
}

.report-table td {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #ecf0f1;
  text-align: right;
}

.report-table tr:nth-child(even) {
  background: #f8f9fa;
}

.report-table tr:hover {
  background: #e3f2fd;
}

/* تحسينات النماذج */
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.date-inputs {
  display: flex;
  gap: 0.5rem;
}

.date-inputs input {
  flex: 1;
  text-align: center;
}

/* تحسينات الأزرار */
.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn:active {
  transform: translateY(0);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.btn-primary {
  background: #87ceeb;
  color: white;
}

.btn-primary:hover {
  background: #4169e1;
}

.btn-secondary {
  background: #87ceeb;
  color: white;
}

.btn-secondary:hover {
  background: #4169e1;
}

.btn-success {
  background: #87ceeb;
  color: white;
}

.btn-success:hover {
  background: #4169e1;
}

.btn-warning {
  background: #87ceeb;
  color: white;
}

.btn-warning:hover {
  background: #4169e1;
}

.btn-danger {
  background: #87ceeb;
  color: white;
}

.btn-danger:hover {
  background: #4169e1;
}

.btn-info {
  background: #87ceeb;
  color: white;
}

.btn-info:hover {
  background: #4169e1;
}
