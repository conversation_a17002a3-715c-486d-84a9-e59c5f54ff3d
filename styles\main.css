:root {
  --primary-color: #3498db;
  --secondary-color: #2ecc71;
  --accent-color: #e74c3c;
  --background-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --text-color: #2c3e50;
  --card-bg: rgba(255, 255, 255, 0.95);
  --border-radius: 12px;
  --shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: var(--background-gradient);
  color: var(--text-color);
  min-height: 100vh;
  line-height: 1.6;
  direction: rtl;
}

/* شريط التنقل */
.main-nav {
  background: var(--card-bg);
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow);
  position: sticky;
  top: 0;
  z-index: 1000;
  margin-bottom: 2rem;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  padding: 1rem 2rem;
  gap: 2rem;
}

.nav-brand h1 {
  color: var(--primary-color);
  font-size: 1.5rem;
  font-weight: 700;
}

.nav-menu {
  display: flex;
  gap: 0.5rem;
  flex: 1;
}

.nav-btn {
  background: transparent;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-radius);
  color: var(--text-color);
  cursor: pointer;
  transition: var(--transition);
  font-weight: 500;
}

.nav-btn:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-2px);
}

.nav-btn.active {
  background: var(--primary-color);
  color: white;
}

.nav-tools {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.search-box {
  display: flex;
  background: white;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-box input {
  border: none;
  padding: 0.5rem 1rem;
  outline: none;
  width: 200px;
}

.search-box button {
  background: var(--primary-color);
  border: none;
  padding: 0.5rem 1rem;
  color: white;
  cursor: pointer;
}

.tool-btn {
  padding: 0.5rem !important;
  min-width: 40px;
}

/* تخطيط الصفحات */
.page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  opacity: 0;
  transform: translateY(20px);
  transition: var(--transition);
}

.page.fade-in {
  opacity: 1;
  transform: translateY(0);
}

.page-container {
  background: var(--card-bg);
  border-radius: var(--border-radius);
  padding: 2rem;
  box-shadow: var(--shadow);
  backdrop-filter: blur(10px);
}

.page-header {
  text-align: center;
  margin-bottom: 2rem;
}

.page-header h2 {
  color: var(--primary-color);
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.page-header p {
  color: #666;
  font-size: 1.1rem;
}

/* الصفحة الرئيسية */
.welcome-container {
  text-align: center;
}

.welcome-header {
  margin-bottom: 3rem;
}

.welcome-header h2 {
  color: var(--primary-color);
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.welcome-header p {
  font-size: 1.2rem;
  color: #666;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.feature-card {
  background: white;
  padding: 2rem;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  cursor: pointer;
  transition: var(--transition);
  text-align: center;
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.feature-card h3 {
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.quick-stats {
  display: flex;
  justify-content: center;
  gap: 3rem;
  margin-top: 2rem;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.stat-label {
  color: #666;
  font-size: 1rem;
}

/* النماذج */
.data-form, .filter-form {
  background: white;
  padding: 2rem;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--text-color);
}

.form-group input,
.form-group textarea,
.form-group select {
  padding: 0.75rem;
  border: 2px solid #e0e0e0;
  border-radius: var(--border-radius);
  font-size: 1rem;
  transition: var(--transition);
  background: white;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.form-group input.error {
  border-color: var(--accent-color);
}

.field-error {
  color: var(--accent-color);
  font-size: 0.875rem;
  margin-top: 0.5rem;
}

.date-inputs {
  display: flex;
  gap: 0.5rem;
}

.date-inputs input {
  flex: 1;
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
}

/* الأزرار */
.btn {
  padding: 0.75rem 2rem;
  border: none;
  border-radius: var(--border-radius);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.btn-primary {
  background: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background: #2980b9;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(52, 152, 219, 0.3);
}

.btn-secondary {
  background: #95a5a6;
  color: white;
}

.btn-secondary:hover {
  background: #7f8c8d;
}

.btn-success {
  background: var(--secondary-color);
  color: white;
}

.btn-success:hover {
  background: #27ae60;
}

.btn-warning {
  background: #f39c12;
  color: white;
}

.btn-warning:hover {
  background: #e67e22;
}

.btn-danger {
  background: var(--accent-color);
  color: white;
}

.btn-danger:hover {
  background: #c0392b;
}

.btn-info {
  background: #3498db;
  color: white;
}

.btn-info:hover {
  background: #2980b9;
}

/* الأرشيف */
.archive-controls {
  display: flex;
  gap: 1rem;
}

.archive-navigation {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin: 2rem 0;
  padding: 1rem;
  background: white;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
}

.record-counter {
  font-weight: 600;
  color: var(--primary-color);
  margin: 0 1rem;
}

.record-display {
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.record-card {
  background: white;
  padding: 2rem;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  width: 100%;
  max-width: 800px;
}

.record-card h3 {
  color: var(--primary-color);
  margin-bottom: 1.5rem;
  text-align: center;
  font-size: 1.5rem;
}

.record-details {
  margin-bottom: 2rem;
}

.record-details p {
  margin-bottom: 1rem;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 8px;
  border-right: 4px solid var(--primary-color);
}

.record-details strong {
  color: var(--primary-color);
  margin-left: 0.5rem;
}

.record-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.no-records {
  text-align: center;
  color: #666;
  font-size: 1.2rem;
}

/* التصفية */
.filter-section {
  background: white;
  padding: 1.5rem;
  border-radius: var(--border-radius);
  margin-bottom: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.filter-section h3 {
  color: var(--primary-color);
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #eee;
}

.date-range {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.filter-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

/* التقارير */
.report-actions {
  display: flex;
  gap: 1rem;
}

.report-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--shadow);
}

.report-table th,
.report-table td {
  padding: 1rem;
  text-align: right;
  border-bottom: 1px solid #eee;
}

.report-table th {
  background: var(--primary-color);
  color: white;
  font-weight: 600;
}

.report-table tr:hover {
  background: #f8f9fa;
}

/* الإعدادات */
.settings-section {
  background: white;
  padding: 2rem;
  border-radius: var(--border-radius);
  margin-bottom: 2rem;
  box-shadow: var(--shadow);
}

.settings-section h3 {
  color: var(--primary-color);
  margin-bottom: 1.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #eee;
}

.theme-selector {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.theme-btn {
  padding: 1rem 2rem;
  border: 3px solid transparent;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-weight: 600;
  min-width: 120px;
}

.default-theme {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.ocean-theme {
  background: linear-gradient(135deg, #1abc9c 0%, #3498db 100%);
  color: white;
}

.sunset-theme {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  color: #2c3e50;
}

.forest-theme {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  color: #2c3e50;
}

.theme-btn:hover {
  transform: scale(1.05);
  border-color: var(--primary-color);
}

.backup-options {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.info-item {
  display: flex;
  justify-content: space-between;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.info-item label {
  font-weight: 600;
  color: var(--primary-color);
}

/* الإشعارات */
.notification {
  position: fixed;
  top: 20px;
  left: 20px;
  background: white;
  padding: 1rem 1.5rem;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  transform: translateX(-100%);
  transition: var(--transition);
  z-index: 10000;
  max-width: 400px;
}

.notification.show {
  transform: translateX(0);
}

.notification-content {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.notification-icon {
  font-size: 1.5rem;
}

.notification-message {
  flex: 1;
}

.notification-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #666;
}

.notification-success {
  border-right: 4px solid var(--secondary-color);
}

.notification-error {
  border-right: 4px solid var(--accent-color);
}

.notification-warning {
  border-right: 4px solid #f39c12;
}

.notification-info {
  border-right: 4px solid var(--primary-color);
}

/* التلميحات */
.tooltip {
  position: absolute;
  background: #333;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-size: 0.875rem;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s;
  z-index: 10000;
}

.tooltip.show {
  opacity: 1;
}

.tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border: 5px solid transparent;
  border-top-color: #333;
}

/* الإحصائيات */
.statistics {
  background: white;
  padding: 2rem;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
}

.statistics h3 {
  color: var(--primary-color);
  text-align: center;
  margin-bottom: 2rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
}

.stat-item {
  text-align: center;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: var(--border-radius);
}

.stat-item h4 {
  font-size: 2.5rem;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
  .nav-container {
    flex-direction: column;
    gap: 1rem;
  }

  .nav-menu {
    order: 3;
    flex-wrap: wrap;
    justify-content: center;
  }

  .page {
    padding: 1rem;
  }

  .page-container {
    padding: 1rem;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .quick-stats {
    flex-direction: column;
    gap: 1rem;
  }

  .date-range {
    grid-template-columns: 1fr;
  }

  .archive-navigation {
    flex-wrap: wrap;
  }

  .theme-selector {
    justify-content: center;
  }

  .backup-options {
    justify-content: center;
  }

  .form-actions {
    flex-direction: column;
  }

  .report-actions {
    flex-direction: column;
  }

  .search-box input {
    width: 150px;
  }
}

@media (max-width: 480px) {
  .nav-brand h1 {
    font-size: 1.2rem;
  }

  .welcome-header h2 {
    font-size: 2rem;
  }

  .page-header h2 {
    font-size: 1.5rem;
  }

  .btn {
    padding: 0.5rem 1rem;
  }
}

/* طباعة */
@media print {
  .main-nav,
  .archive-navigation,
  .record-actions,
  .form-actions,
  .report-actions,
  .page-header {
    display: none !important;
  }

  .page {
    box-shadow: none;
    background: white;
    margin: 0;
    padding: 1rem;
  }

  .page-container {
    box-shadow: none;
    background: white;
  }

  .report-table {
    box-shadow: none;
  }
}
