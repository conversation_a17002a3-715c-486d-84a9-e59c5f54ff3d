// وحدة واجهة المستخدم المتقدمة
class UIManager {
  constructor() {
    this.currentPage = 'welcomePage';
    this.animations = {
      fadeIn: 'fadeIn 0.5s ease-in',
      slideIn: 'slideIn 0.5s ease-out',
      bounce: 'bounce 0.6s ease-in-out',
      pulse: 'pulse 1s infinite'
    };

    this.init();
  }

  init() {
    this.setupEventListeners();
    this.initializeComponents();
  }

  setupEventListeners() {
    // إضافة مستمعات الأحداث للنماذج
    document.addEventListener('DOMContentLoaded', () => {
      this.setupFormValidation();
      this.setupKeyboardShortcuts();
      this.setupTooltips();
    });

    // مستمع تغيير حجم النافذة
    window.addEventListener('resize', () => {
      this.handleResize();
    });
  }

  // إعداد التحقق من صحة النماذج
  setupFormValidation() {
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
      form.addEventListener('submit', (e) => {
        if (!this.validateForm(form)) {
          e.preventDefault();
          this.showValidationErrors(form);
        }
      });

      // التحقق الفوري أثناء الكتابة
      const inputs = form.querySelectorAll('input, textarea, select');
      inputs.forEach(input => {
        input.addEventListener('blur', () => {
          this.validateField(input);
        });
      });
    });
  }

  // التحقق من صحة النموذج
  validateForm(form) {
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;

    requiredFields.forEach(field => {
      if (!this.validateField(field)) {
        isValid = false;
      }
    });

    return isValid;
  }

  // التحقق من صحة حقل واحد
  validateField(field) {
    const value = field.value.trim();
    const fieldType = field.type;
    let isValid = true;
    let errorMessage = '';

    // التحقق من الحقول المطلوبة
    if (field.required && !value) {
      isValid = false;
      errorMessage = 'هذا الحقل مطلوب';
    }

    // تحقق خاص بنوع الحقل
    if (value && fieldType === 'number') {
      const num = parseInt(value);
      if (isNaN(num) || num < 1) {
        isValid = false;
        errorMessage = 'يرجى إدخال رقم صحيح';
      }
    }

    // عرض أو إخفاء رسالة الخطأ
    this.toggleFieldError(field, isValid, errorMessage);
    return isValid;
  }

  // عرض أو إخفاء خطأ الحقل
  toggleFieldError(field, isValid, message) {
    const errorElement = field.parentNode.querySelector('.field-error');

    if (!isValid) {
      field.classList.add('error');
      if (!errorElement) {
        const error = document.createElement('div');
        error.className = 'field-error';
        error.textContent = message;
        field.parentNode.appendChild(error);
      } else {
        errorElement.textContent = message;
      }
    } else {
      field.classList.remove('error');
      if (errorElement) {
        errorElement.remove();
      }
    }
  }

  // إعداد اختصارات لوحة المفاتيح
  setupKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
      // Ctrl + S للحفظ
      if (e.ctrlKey && e.key === 's') {
        e.preventDefault();
        this.quickSave();
      }

      // Ctrl + F للبحث
      if (e.ctrlKey && e.key === 'f') {
        e.preventDefault();
        this.focusSearchInput();
      }

      // ESC للعودة للصفحة الرئيسية
      if (e.key === 'Escape') {
        this.goHome();
      }

      // أسهم التنقل في الأرشيف
      if (this.currentPage === 'archivePage') {
        if (e.key === 'ArrowLeft') {
          e.preventDefault();
          nextRecord();
        } else if (e.key === 'ArrowRight') {
          e.preventDefault();
          prevRecord();
        }
      }
    });
  }

  // إعداد التلميحات
  setupTooltips() {
    const tooltipElements = document.querySelectorAll('[data-tooltip]');
    tooltipElements.forEach(element => {
      element.addEventListener('mouseenter', (e) => {
        this.showTooltip(e.target, e.target.dataset.tooltip);
      });

      element.addEventListener('mouseleave', () => {
        this.hideTooltip();
      });
    });
  }

  // عرض التلميح
  showTooltip(element, text) {
    const tooltip = document.createElement('div');
    tooltip.className = 'tooltip';
    tooltip.textContent = text;
    document.body.appendChild(tooltip);

    const rect = element.getBoundingClientRect();
    tooltip.style.left = `${rect.left + rect.width / 2}px`;
    tooltip.style.top = `${rect.top - tooltip.offsetHeight - 10}px`;
    tooltip.style.transform = 'translateX(-50%)';

    setTimeout(() => tooltip.classList.add('show'), 10);
  }

  // إخفاء التلميح
  hideTooltip() {
    const tooltip = document.querySelector('.tooltip');
    if (tooltip) {
      tooltip.remove();
    }
  }

  // تهيئة المكونات
  initializeComponents() {
    this.setupProgressBars();
    this.setupModalWindows();
    this.setupDropdowns();
  }

  // إعداد أشرطة التقدم
  setupProgressBars() {
    const progressBars = document.querySelectorAll('.progress-bar');
    progressBars.forEach(bar => {
      const progress = bar.dataset.progress || 0;
      this.animateProgressBar(bar, progress);
    });
  }

  // تحريك شريط التقدم
  animateProgressBar(bar, targetProgress) {
    let currentProgress = 0;
    const increment = targetProgress / 50;

    const animate = () => {
      if (currentProgress < targetProgress) {
        currentProgress += increment;
        bar.style.width = `${Math.min(currentProgress, targetProgress)}%`;
        requestAnimationFrame(animate);
      }
    };

    animate();
  }

  // إعداد النوافذ المنبثقة
  setupModalWindows() {
    // إغلاق النافذة بالنقر خارجها
    document.addEventListener('click', (e) => {
      if (e.target.classList.contains('modal')) {
        this.closeModal(e.target);
      }
    });

    // إغلاق النافذة بمفتاح ESC
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        const openModal = document.querySelector('.modal.show');
        if (openModal) {
          this.closeModal(openModal);
        }
      }
    });
  }

  // فتح نافذة منبثقة
  openModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
      modal.classList.add('show');
      document.body.style.overflow = 'hidden';
      this.playAnimation(modal, 'fadeIn');
    }
  }

  // إغلاق نافذة منبثقة
  closeModal(modal) {
    modal.classList.remove('show');
    document.body.style.overflow = 'auto';
  }

  // إعداد القوائم المنسدلة
  setupDropdowns() {
    const dropdowns = document.querySelectorAll('.dropdown');
    dropdowns.forEach(dropdown => {
      const trigger = dropdown.querySelector('.dropdown-trigger');
      const menu = dropdown.querySelector('.dropdown-menu');

      trigger.addEventListener('click', () => {
        menu.classList.toggle('show');
      });

      // إغلاق القائمة عند النقر خارجها
      document.addEventListener('click', (e) => {
        if (!dropdown.contains(e.target)) {
          menu.classList.remove('show');
        }
      });
    });
  }

  // تشغيل الرسوم المتحركة
  playAnimation(element, animationName) {
    element.style.animation = this.animations[animationName];

    element.addEventListener('animationend', () => {
      element.style.animation = '';
    }, { once: true });
  }

  // إنشاء رسالة تأكيد مخصصة
  showConfirmDialog(message, onConfirm, onCancel) {
    const dialog = document.createElement('div');
    dialog.className = 'confirm-dialog modal';
    dialog.innerHTML = `
      <div class="modal-content">
        <div class="confirm-message">${message}</div>
        <div class="confirm-buttons">
          <button class="btn btn-primary confirm-yes">نعم</button>
          <button class="btn btn-secondary confirm-no">لا</button>
        </div>
      </div>
    `;

    document.body.appendChild(dialog);
    dialog.classList.add('show');

    dialog.querySelector('.confirm-yes').addEventListener('click', () => {
      onConfirm && onConfirm();
      this.closeModal(dialog);
      dialog.remove();
    });

    dialog.querySelector('.confirm-no').addEventListener('click', () => {
      onCancel && onCancel();
      this.closeModal(dialog);
      dialog.remove();
    });
  }

  // حفظ سريع
  quickSave() {
    if (this.currentPage === 'dataEntryPage') {
      addRecord();
    }
  }

  // التركيز على حقل البحث
  focusSearchInput() {
    const searchInput = document.getElementById('quickSearchInput');
    if (searchInput) {
      searchInput.focus();
      searchInput.select();
    }
  }

  // العودة للصفحة الرئيسية
  goHome() {
    showPage('welcomePage');
  }

  // معالجة تغيير حجم النافذة
  handleResize() {
    // تحديث تخطيط الشبكة
    const grids = document.querySelectorAll('.responsive-grid');
    grids.forEach(grid => {
      this.updateGridLayout(grid);
    });
  }

  // تحديث تخطيط الشبكة
  updateGridLayout(grid) {
    const width = grid.offsetWidth;
    const columns = width < 768 ? 1 : width < 1024 ? 2 : 3;
    grid.style.gridTemplateColumns = `repeat(${columns}, 1fr)`;
  }

  // إنشاء محرر نصوص متقدم
  createRichTextEditor(containerId) {
    const container = document.getElementById(containerId);
    if (!container) return;

    const toolbar = document.createElement('div');
    toolbar.className = 'rich-text-toolbar';
    toolbar.innerHTML = `
      <button type="button" onclick="document.execCommand('bold')"><b>B</b></button>
      <button type="button" onclick="document.execCommand('italic')"><i>I</i></button>
      <button type="button" onclick="document.execCommand('underline')"><u>U</u></button>
      <button type="button" onclick="document.execCommand('insertOrderedList')">1.</button>
      <button type="button" onclick="document.execCommand('insertUnorderedList')">•</button>
    `;

    const editor = document.createElement('div');
    editor.className = 'rich-text-editor';
    editor.contentEditable = true;
    editor.innerHTML = container.innerHTML;

    container.innerHTML = '';
    container.appendChild(toolbar);
    container.appendChild(editor);

    return editor;
  }

  // إدارة السجلات المفضلة
  toggleFavorite(recordId) {
    const favorites = JSON.parse(localStorage.getItem('favoriteRecords') || '[]');
    const index = favorites.indexOf(recordId);

    if (index === -1) {
      favorites.push(recordId);
      this.showNotification('تم إضافة السجل للمفضلة', 'success');
    } else {
      favorites.splice(index, 1);
      this.showNotification('تم إزالة السجل من المفضلة', 'info');
    }

    localStorage.setItem('favoriteRecords', JSON.stringify(favorites));
    this.updateFavoriteButton(recordId, favorites.includes(recordId));
  }

  // تحديث زر المفضلة
  updateFavoriteButton(recordId, isFavorite) {
    const button = document.querySelector(`[data-record-id="${recordId}"] .favorite-btn`);
    if (button) {
      button.innerHTML = isFavorite ? '★' : '☆';
      button.classList.toggle('active', isFavorite);
    }
  }

  // عرض رسالة التنبيه
  showNotification(message, type = 'info', duration = 3000) {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;

    const icon = this.getNotificationIcon(type);
    notification.innerHTML = `
      <div class="notification-content">
        <span class="notification-icon">${icon}</span>
        <span class="notification-message">${message}</span>
        <button class="notification-close">&times;</button>
      </div>
    `;

    document.body.appendChild(notification);

    // إضافة تأثير الظهور
    setTimeout(() => notification.classList.add('show'), 100);

    // إزالة التنبيه تلقائياً
    const autoRemove = setTimeout(() => {
      this.removeNotification(notification);
    }, duration);

    // إضافة زر الإغلاق
    notification.querySelector('.notification-close').addEventListener('click', () => {
      clearTimeout(autoRemove);
      this.removeNotification(notification);
    });
  }

  // إزالة التنبيه
  removeNotification(notification) {
    notification.classList.remove('show');
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 300);
  }

  // الحصول على أيقونة التنبيه
  getNotificationIcon(type) {
    const icons = {
      success: '✓',
      error: '✗',
      warning: '⚠',
      info: 'ℹ'
    };
    return icons[type] || icons.info;
  }
}

// إنشاء مثيل من مدير واجهة المستخدم
const uiManager = new UIManager();
