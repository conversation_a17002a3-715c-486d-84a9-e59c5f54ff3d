<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>برنامج الأرشفة الإلكترونية</title>
  <link rel="stylesheet" href="styles/main.css">
  <link rel="stylesheet" href="styles/themes.css">
  <link rel="stylesheet" href="styles/animations.css">
  <link rel="icon" href="assets/favicon.ico" type="image/x-icon">
</head>
<body>
  <!-- شريط التنقل الرئيسي -->
  <nav class="main-nav">
    <div class="nav-container">
      <div class="nav-brand">
        <h1>📚 برنامج الأرشفة الإلكترونية</h1>
      </div>
      <div class="nav-menu">
        <button onclick="showPage('welcomePage')" class="nav-btn active">الرئيسية</button>
        <button onclick="showPage('dataEntryPage')" class="nav-btn">إدخال البيانات</button>
        <button onclick="showPage('archivePage')" class="nav-btn">الأرشيف</button>
        <button onclick="showPage('filterPage')" class="nav-btn">البحث المتقدم</button>
        <button onclick="showPage('reportPage')" class="nav-btn">التقارير</button>
        <button onclick="showPage('settingsPage')" class="nav-btn">الإعدادات</button>
      </div>
      <div class="nav-tools">
        <div class="search-box">
          <input type="text" id="quickSearchInput" placeholder="بحث سريع..." onkeyup="if(event.key==='Enter') quickSearch()">
          <button onclick="quickSearch()">🔍</button>
        </div>
        <button onclick="showStatistics()" class="nav-btn tool-btn" data-tooltip="الإحصائيات">📊</button>
        <button onclick="exportData()" class="nav-btn tool-btn" data-tooltip="تصدير البيانات">💾</button>
      </div>
    </div>
  </nav>

  <!-- الصفحة الرئيسية -->
  <div id="welcomePage" class="page">
    <div class="welcome-container">
      <div class="welcome-header">
        <h2>مرحباً بك في برنامج الأرشفة الإلكترونية</h2>
        <p>نظام متكامل لإدارة وأرشفة الوثائق والمستندات الرسمية</p>
      </div>

      <div class="features-grid">
        <div class="feature-card" onclick="showPage('dataEntryPage')">
          <div class="feature-icon">📝</div>
          <h3>إدخال البيانات</h3>
          <p>إضافة سجلات جديدة للأرشيف</p>
        </div>

        <div class="feature-card" onclick="showPage('archivePage')">
          <div class="feature-icon">📁</div>
          <h3>تصفح الأرشيف</h3>
          <p>عرض وإدارة السجلات المحفوظة</p>
        </div>

        <div class="feature-card" onclick="showPage('filterPage')">
          <div class="feature-icon">🔍</div>
          <h3>البحث المتقدم</h3>
          <p>البحث والتصفية بمعايير متعددة</p>
        </div>

        <div class="feature-card" onclick="showPage('reportPage')">
          <div class="feature-icon">📊</div>
          <h3>التقارير</h3>
          <p>عرض تقارير شاملة للبيانات</p>
        </div>
      </div>

      <div class="quick-stats">
        <div class="stat-item">
          <span class="stat-number" id="totalRecordsCount">0</span>
          <span class="stat-label">إجمالي السجلات</span>
        </div>
        <div class="stat-item">
          <span class="stat-number" id="todayRecordsCount">0</span>
          <span class="stat-label">سجلات اليوم</span>
        </div>
        <div class="stat-item">
          <span class="stat-number" id="departmentsCount">0</span>
          <span class="stat-label">عدد الدوائر</span>
        </div>
      </div>
    </div>
  </div>

  <!-- صفحة إدخال البيانات -->
  <div id="dataEntryPage" class="page" style="display: none;">
    <div class="page-container">
      <div class="page-header">
        <h2>إدخال بيانات جديدة</h2>
        <p>يرجى ملء جميع الحقول المطلوبة بدقة</p>
      </div>

      <form id="dataForm" class="data-form">
        <div class="form-grid">
          <div class="form-group">
            <label for="bookNumber">رقم الكتاب *</label>
            <input type="text" id="bookNumber" required placeholder="مثال: 123/2024">
          </div>

          <div class="form-group date-group">
            <label>التاريخ *</label>
            <div class="date-inputs">
              <input type="number" id="day" min="1" max="31" required placeholder="يوم">
              <input type="number" id="month" min="1" max="12" required placeholder="شهر">
              <input type="number" id="year" min="1900" max="2100" required placeholder="سنة">
            </div>
          </div>

          <div class="form-group full-width">
            <label for="subject">الموضوع *</label>
            <input type="text" id="subject" required placeholder="موضوع الكتاب">
          </div>

          <div class="form-group full-width">
            <label for="content">المحتوى *</label>
            <textarea id="content" rows="4" required placeholder="محتوى الكتاب"></textarea>
          </div>

          <div class="form-group">
            <label for="department">الدائرة</label>
            <select id="department">
              <option value="">اختر الدائرة</option>
              <option value="الإدارة العامة">الإدارة العامة</option>
              <option value="الموارد البشرية">الموارد البشرية</option>
              <option value="المالية">المالية</option>
              <option value="القانونية">القانونية</option>
              <option value="التقنية">التقنية</option>
              <option value="العلاقات العامة">العلاقات العامة</option>
              <option value="أخرى">أخرى</option>
            </select>
          </div>

          <div class="form-group full-width">
            <label for="notes">الهامش</label>
            <textarea id="notes" rows="3" placeholder="ملاحظات إضافية (اختياري)"></textarea>
          </div>
        </div>

        <div class="form-actions">
          <button type="button" onclick="addRecord()" class="btn btn-primary">
            حفظ السجل
          </button>
          <button type="reset" class="btn btn-secondary">
            مسح النموذج
          </button>
        </div>
      </form>
    </div>
  </div>

  <!-- صفحة الأرشيف -->
  <div id="archivePage" class="page" style="display: none;">
    <div class="page-container">
      <div class="page-header">
        <h2>تصفح الأرشيف</h2>
        <div class="archive-controls">
          <button onclick="showDetailedStatistics()" class="btn btn-info">الإحصائيات التفصيلية</button>
        <button onclick="showSearchHistory()" class="btn btn-secondary">سجل البحث</button>
        <button onclick="showActivityLog()" class="btn btn-warning">سجل العمليات</button>
          <button onclick="clearFilter()" class="btn btn-secondary">عرض الكل</button>
        </div>
      </div>

      <div class="archive-navigation">
        <button id="firstBtn" onclick="firstRecord()" class="nav-btn">الأول</button>
        <button id="prevBtn" onclick="prevRecord()" class="nav-btn">السابق</button>
        <span id="recordCounter" class="record-counter">0 من 0</span>
        <button id="nextBtn" onclick="nextRecord()" class="nav-btn">التالي</button>
        <button id="lastBtn" onclick="lastRecord()" class="nav-btn">الأخير</button>
      </div>

      <div id="recordDisplay" class="record-display">
        <p class="no-records">لا توجد سجلات للعرض</p>
      </div>
    </div>
  </div>

  <!-- صفحة البحث والتصفية -->
  <div id="filterPage" class="page" style="display: none;">
    <div class="page-container">
      <div class="page-header">
        <h2>البحث المتقدم والتصفية</h2>
        <p>استخدم المعايير التالية للبحث في الأرشيف</p>
      </div>

      <form id="filterForm" class="filter-form">
        <div class="filter-section">
          <h3>تصفية بالتاريخ</h3>
          <div class="date-range">
            <div class="date-group">
              <label>من تاريخ:</label>
              <div class="date-inputs">
                <input type="number" id="fromDay" min="1" max="31" placeholder="يوم">
                <input type="number" id="fromMonth" min="1" max="12" placeholder="شهر">
                <input type="number" id="fromYear" min="1900" max="2100" placeholder="سنة">
              </div>
            </div>
            <div class="date-group">
              <label>إلى تاريخ:</label>
              <div class="date-inputs">
                <input type="number" id="toDay" min="1" max="31" placeholder="يوم">
                <input type="number" id="toMonth" min="1" max="12" placeholder="شهر">
                <input type="number" id="toYear" min="1900" max="2100" placeholder="سنة">
              </div>
            </div>
          </div>
        </div>

        <div class="filter-section">
          <h3>تصفية بالمحتوى</h3>
          <div class="form-grid">
            <div class="form-group">
              <label for="bookNumberFilter">رقم الكتاب</label>
              <input type="text" id="bookNumberFilter" placeholder="البحث برقم الكتاب">
            </div>
            <div class="form-group">
              <label for="subjectFilter">الموضوع</label>
              <input type="text" id="subjectFilter" placeholder="البحث في الموضوع">
            </div>
            <div class="form-group">
              <label for="employeeFilter">اسم الموظف</label>
              <input type="text" id="employeeFilter" placeholder="البحث في المحتوى">
            </div>
            <div class="form-group">
              <label for="departmentFilter">الدائرة</label>
              <input type="text" id="departmentFilter" placeholder="البحث في الدائرة">
            </div>
            <div class="form-group">
              <label for="notesFilter">الهامش</label>
              <select id="notesFilter">
                <option value="">الكل</option>
                <option value="has-notes">يحتوي على هوامش</option>
                <option value="no-notes">لا يحتوي على هوامش</option>
                <option value="important-notes">هوامش مهمة</option>
              </select>
            </div>
          </div>
        </div>

        <div class="filter-actions">
          <button type="button" onclick="applyFilter()" class="btn btn-primary">
            تطبيق التصفية
          </button>
          <button type="button" onclick="clearFilter()" class="btn btn-secondary">
            إلغاء التصفية
          </button>
          <button type="button" onclick="restoreFilterCriteria()" class="btn btn-info">
            استعادة آخر تصفية
          </button>
        </div>
      </form>
    </div>
  </div>

  <!-- صفحة التقارير -->
  <div id="reportPage" class="page" style="display: none;">
    <div class="page-container">
      <div class="page-header">
        <h2>تقارير شاملة</h2>
        <div class="report-actions">
          <button onclick="window.print()" class="btn btn-info">طباعة</button>
          <button onclick="exportData()" class="btn btn-success">تصدير</button>
        </div>
      </div>

      <div class="report-content">
        <table class="report-table">
          <thead>
            <tr>
              <th>م</th>
              <th>رقم الكتاب</th>
              <th>التاريخ</th>
              <th>الموضوع</th>
              <th>المحتوى</th>
              <th>الدائرة</th>
              <th>الهامش</th>
            </tr>
          </thead>
          <tbody id="reportTableBody">
            <!-- سيتم ملء البيانات هنا -->
          </tbody>
        </table>
      </div>
    </div>
  </div>

  <!-- صفحة الإعدادات -->
  <div id="settingsPage" class="page" style="display: none;">
    <div class="page-container">
      <div class="page-header">
        <h2>الإعدادات</h2>
        <p>تخصيص إعدادات البرنامج</p>
      </div>

      <div class="settings-content">
        <div class="settings-section">
          <h3>المظهر</h3>
          <div class="theme-selector">
            <button onclick="applyTheme('default')" class="theme-btn default-theme">افتراضي</button>
            <button onclick="applyTheme('ocean')" class="theme-btn ocean-theme">محيطي</button>
            <button onclick="applyTheme('sunset')" class="theme-btn sunset-theme">غروب</button>
            <button onclick="applyTheme('forest')" class="theme-btn forest-theme">غابة</button>
          </div>
        </div>

        <div class="settings-section">
          <h3>النسخ الاحتياطي</h3>
          <div class="backup-options">
            <button onclick="exportData()" class="btn btn-primary">تصدير البيانات</button>
            <input type="file" id="importFile" accept=".json" onchange="importData(event)" style="display: none;">
            <button onclick="document.getElementById('importFile').click()" class="btn btn-secondary">استيراد البيانات</button>
          </div>
        </div>

        <div class="settings-section">
          <h3>معلومات البرنامج</h3>
          <div class="info-grid">
            <div class="info-item">
              <label>الإصدار:</label>
              <span>1.0.0</span>
            </div>
            <div class="info-item">
              <label>آخر تحديث:</label>
              <span id="lastUpdate">-</span>
            </div>
            <div class="info-item">
              <label>حجم قاعدة البيانات:</label>
              <span id="dbSize">-</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- تحميل السكريبتات -->
  <script src="scripts/database.js"></script>
  <script src="scripts/ui.js"></script>
  <script src="scripts/app.js"></script>

  <!-- تحديث الإحصائيات عند التحميل -->
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // تحديث إحصائيات الصفحة الرئيسية
      const totalRecords = archiveData.length;
      const today = new Date();
      const todayRecords = archiveData.filter(record => {
        const recordDate = new Date(record.year, record.month - 1, record.day);
        return recordDate.toDateString() === today.toDateString();
      }).length;
      const departments = [...new Set(archiveData.map(r => r.department))].filter(Boolean).length;

      document.getElementById('totalRecordsCount').textContent = totalRecords;
      document.getElementById('todayRecordsCount').textContent = todayRecords;
      document.getElementById('departmentsCount').textContent = departments;

      // تحديث معلومات البرنامج
      const dataSize = JSON.stringify(archiveData).length;
      document.getElementById('dbSize').textContent = `${(dataSize / 1024).toFixed(2)} KB`;
      document.getElementById('lastUpdate').textContent = new Date().toLocaleDateString('ar-SA');
    });
  </script>
</body>
</html>
