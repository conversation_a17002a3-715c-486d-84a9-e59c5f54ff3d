<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="description" content="برنامج الأرشفة الإلكترونية - نظام متكامل لإدارة وأرشفة الوثائق">
  <meta name="keywords" content="أرشفة, وثائق, إدارة, سجلات, إلكترونية">
  <meta name="author" content="المحاسب المبرمج علي عاجل خشان المحنة">
  <meta name="robots" content="noindex, nofollow">

  <title>برنامج الأرشفة الإلكترونية</title>

  <!-- CSS Files -->
  <link rel="stylesheet" href="styles/main.css">
  <link rel="stylesheet" href="styles/themes.css">
  <link rel="stylesheet" href="styles/animations.css">

  <!-- Favicon -->
  <link rel="icon" href="assets/favicon.ico" type="image/x-icon">
  <link rel="shortcut icon" href="assets/favicon.ico" type="image/x-icon">

  <!-- Web App Manifest for PWA support -->
  <link rel="manifest" href="manifest.json">

  <!-- iOS Safari specific -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="default">
  <meta name="apple-mobile-web-app-title" content="الأرشفة الإلكترونية">

  <!-- Android Chrome specific -->
  <meta name="theme-color" content="#3498db">
  <meta name="mobile-web-app-capable" content="yes">

  <!-- Preload critical resources -->
  <link rel="preload" href="scripts/app.js" as="script">
  <link rel="preload" href="styles/main.css" as="style">
</head>
<body>
  <!-- شريط التنقل الرئيسي -->
  <nav class="main-nav">
    <div class="nav-container">
      <div class="nav-brand">
        <h1>📚 برنامج الأرشفة الإلكترونية</h1>
      </div>
      <div class="nav-menu">
        <button onclick="showPage('welcomePage')" class="nav-btn active">الرئيسية</button>
        <button onclick="showPage('dataEntryPage')" class="nav-btn">إدخال البيانات</button>
        <button onclick="showPage('archivePage')" class="nav-btn">الأرشيف</button>
        <button onclick="showPage('filterPage')" class="nav-btn">البحث المتقدم</button>
        <button onclick="showPage('reportPage')" class="nav-btn">التقارير</button>
        <button onclick="showPage('settingsPage')" class="nav-btn">الإعدادات</button>
      </div>
      <div class="nav-tools">
        <div class="search-box">
          <input type="text" id="quickSearchInput" placeholder="بحث سريع..." onkeyup="if(event.key==='Enter') quickSearch()">
          <button onclick="quickSearch()">🔍</button>
        </div>
        <button onclick="showStatistics()" class="nav-btn tool-btn" data-tooltip="الإحصائيات">📊</button>
        <button onclick="exportData()" class="nav-btn tool-btn" data-tooltip="تصدير البيانات">💾</button>
      </div>
    </div>
  </nav>

  <!-- الصفحة الرئيسية -->
  <div id="welcomePage" class="page">
    <div class="welcome-container">
      <div class="welcome-header">
        <h2>مرحباً بك في برنامج الأرشفة الإلكترونية</h2>
        <p>نظام متكامل لإدارة وأرشفة الوثائق والمستندات الرسمية</p>
      </div>

      <div class="features-grid">
        <div class="feature-card" onclick="showPage('dataEntryPage')">
          <div class="feature-icon">📝</div>
          <h3>إدخال البيانات</h3>
          <p>إضافة سجلات جديدة للأرشيف</p>
        </div>

        <div class="feature-card" onclick="showPage('archivePage')">
          <div class="feature-icon">📁</div>
          <h3>تصفح الأرشيف</h3>
          <p>عرض وإدارة السجلات المحفوظة</p>
        </div>

        <div class="feature-card" onclick="showPage('filterPage')">
          <div class="feature-icon">🔍</div>
          <h3>البحث المتقدم</h3>
          <p>البحث والتصفية بمعايير متعددة</p>
        </div>

        <div class="feature-card" onclick="showPage('reportPage')">
          <div class="feature-icon">📊</div>
          <h3>التقارير</h3>
          <p>عرض تقارير شاملة للبيانات</p>
        </div>
      </div>

      <div class="quick-stats">
        <div class="stat-item">
          <span class="stat-number" id="totalRecordsCount">0</span>
          <span class="stat-label">إجمالي السجلات</span>
        </div>
        <div class="stat-item">
          <span class="stat-number" id="todayRecordsCount">0</span>
          <span class="stat-label">سجلات اليوم</span>
        </div>
        <div class="stat-item">
          <span class="stat-number" id="departmentsCount">0</span>
          <span class="stat-label">عدد الدوائر</span>
        </div>
      </div>
    </div>
  </div>

  <!-- صفحة إدخال البيانات -->
  <div id="dataEntryPage" class="page" style="display: none;">
    <div class="page-container">
      <div class="page-header">
        <h2>إدخال بيانات جديدة</h2>
        <p>يرجى ملء جميع الحقول المطلوبة بدقة</p>
      </div>

      <form id="dataForm" class="data-form">
        <div class="form-grid">
          <div class="form-group">
            <label for="bookNumber">رقم الكتاب *</label>
            <input type="text" id="bookNumber" required placeholder="مثال: 123/2024">
          </div>

          <div class="form-group date-group">
            <label>التاريخ *</label>
            <div class="date-inputs">
              <input type="number" id="day" min="1" max="31" required placeholder="يوم">
              <input type="number" id="month" min="1" max="12" required placeholder="شهر">
              <input type="number" id="year" min="1900" max="2100" required placeholder="سنة">
            </div>
          </div>

          <div class="form-group full-width">
            <label for="subject">الموضوع *</label>
            <input type="text" id="subject" required placeholder="موضوع الكتاب">
          </div>

          <div class="form-group full-width">
            <label for="content">المحتوى *</label>
            <textarea id="content" rows="4" required placeholder="محتوى الكتاب"></textarea>
          </div>

          <div class="form-group">
            <label for="department">الدائرة</label>
            <select id="department">
              <option value="">اختر الدائرة</option>
              <option value="الإدارة العامة">الإدارة العامة</option>
              <option value="الموارد البشرية">الموارد البشرية</option>
              <option value="المالية">المالية</option>
              <option value="القانونية">القانونية</option>
              <option value="التقنية">التقنية</option>
              <option value="العلاقات العامة">العلاقات العامة</option>
              <option value="أخرى">أخرى</option>
            </select>
          </div>

          <div class="form-group full-width">
            <label for="notes">الهامش</label>
            <textarea id="notes" rows="3" placeholder="ملاحظات إضافية (اختياري)"></textarea>
          </div>
        </div>

        <div class="form-actions">
          <button type="button" onclick="addRecord()" class="btn btn-primary">
            حفظ السجل
          </button>
          <button type="reset" class="btn btn-secondary">
            مسح النموذج
          </button>
        </div>
      </form>
    </div>
  </div>

  <!-- صفحة الأرشيف -->
  <div id="archivePage" class="page" style="display: none;">
    <div class="page-container">
      <div class="page-header">
        <h2>تصفح الأرشيف</h2>
        <div class="archive-controls">
          <button onclick="showDetailedStatistics()" class="btn btn-info">الإحصائيات التفصيلية</button>
        <button onclick="showSearchHistory()" class="btn btn-secondary">سجل البحث</button>
        <button onclick="showActivityLog()" class="btn btn-warning">سجل العمليات</button>
          <button onclick="clearFilter()" class="btn btn-secondary">عرض الكل</button>
        </div>
      </div>

      <div class="archive-navigation">
        <button id="firstBtn" onclick="firstRecord()" class="nav-btn">الأول</button>
        <button id="prevBtn" onclick="prevRecord()" class="nav-btn">السابق</button>
        <span id="recordCounter" class="record-counter">0 من 0</span>
        <button id="nextBtn" onclick="nextRecord()" class="nav-btn">التالي</button>
        <button id="lastBtn" onclick="lastRecord()" class="nav-btn">الأخير</button>
      </div>

      <div id="recordDisplay" class="record-display">
        <p class="no-records">لا توجد سجلات للعرض</p>
      </div>
    </div>
  </div>

  <!-- صفحة البحث والتصفية -->
  <div id="filterPage" class="page" style="display: none;">
    <div class="page-container">
      <div class="page-header">
        <h2>البحث المتقدم والتصفية</h2>
        <p>استخدم المعايير التالية للبحث في الأرشيف</p>
      </div>

      <form id="filterForm" class="filter-form">
        <div class="filter-section">
          <h3>تصفية بالتاريخ</h3>
          <div class="date-range">
            <div class="date-group">
              <label>من تاريخ:</label>
              <div class="date-inputs">
                <input type="number" id="fromDay" min="1" max="31" placeholder="يوم">
                <input type="number" id="fromMonth" min="1" max="12" placeholder="شهر">
                <input type="number" id="fromYear" min="1900" max="2100" placeholder="سنة">
              </div>
            </div>
            <div class="date-group">
              <label>إلى تاريخ:</label>
              <div class="date-inputs">
                <input type="number" id="toDay" min="1" max="31" placeholder="يوم">
                <input type="number" id="toMonth" min="1" max="12" placeholder="شهر">
                <input type="number" id="toYear" min="1900" max="2100" placeholder="سنة">
              </div>
            </div>
          </div>
        </div>

        <div class="filter-section">
          <h3>تصفية بالمحتوى</h3>
          <div class="form-grid">
            <div class="form-group">
              <label for="bookNumberFilter">رقم الكتاب</label>
              <input type="text" id="bookNumberFilter" placeholder="البحث برقم الكتاب">
            </div>
            <div class="form-group">
              <label for="subjectFilter">الموضوع</label>
              <input type="text" id="subjectFilter" placeholder="البحث في الموضوع">
            </div>
            <div class="form-group">
              <label for="employeeFilter">اسم الموظف</label>
              <input type="text" id="employeeFilter" placeholder="البحث في المحتوى">
            </div>
            <div class="form-group">
              <label for="departmentFilter">الدائرة</label>
              <input type="text" id="departmentFilter" placeholder="البحث في الدائرة">
            </div>
            <div class="form-group">
              <label for="notesFilter">الهامش</label>
              <select id="notesFilter">
                <option value="">الكل</option>
                <option value="has-notes">يحتوي على هوامش</option>
                <option value="no-notes">لا يحتوي على هوامش</option>
                <option value="important-notes">هوامش مهمة</option>
              </select>
            </div>
          </div>
        </div>

        <div class="filter-actions">
          <button type="button" onclick="applyFilter()" class="btn btn-primary">
            تطبيق التصفية
          </button>
          <button type="button" onclick="clearFilter()" class="btn btn-secondary">
            إلغاء التصفية
          </button>
          <button type="button" onclick="restoreFilterCriteria()" class="btn btn-info">
            استعادة آخر تصفية
          </button>
        </div>
      </form>
    </div>
  </div>

  <!-- صفحة التقارير -->
  <div id="reportPage" class="page" style="display: none;">
    <div class="page-container">
      <div class="page-header">
        <h2>تقارير شاملة</h2>
        <div class="report-actions">
          <button onclick="window.print()" class="btn btn-info">طباعة</button>
          <button onclick="exportData()" class="btn btn-success">تصدير</button>
        </div>
      </div>

      <div class="report-content">
        <table class="report-table">
          <thead>
            <tr>
              <th>م</th>
              <th>رقم الكتاب</th>
              <th>التاريخ</th>
              <th>الموضوع</th>
              <th>المحتوى</th>
              <th>الدائرة</th>
              <th>الهامش</th>
            </tr>
          </thead>
          <tbody id="reportTableBody">
            <!-- سيتم ملء البيانات هنا -->
          </tbody>
        </table>
      </div>
    </div>
  </div>

  <!-- صفحة الإعدادات -->
  <div id="settingsPage" class="page" style="display: none;">
    <div class="page-container">
      <div class="page-header">
        <h2>الإعدادات</h2>
        <p>تخصيص إعدادات البرنامج</p>
      </div>

      <div class="settings-content">
        <div class="settings-section">
          <h3>المظهر</h3>
          <div class="theme-selector">
            <button onclick="applyTheme('default')" class="theme-btn default-theme">افتراضي</button>
            <button onclick="applyTheme('ocean')" class="theme-btn ocean-theme">محيطي</button>
            <button onclick="applyTheme('sunset')" class="theme-btn sunset-theme">غروب</button>
            <button onclick="applyTheme('forest')" class="theme-btn forest-theme">غابة</button>
          </div>
        </div>

        <div class="settings-section">
          <h3>النسخ الاحتياطي</h3>
          <div class="backup-options">
            <button onclick="exportData()" class="btn btn-primary">تصدير البيانات</button>
            <input type="file" id="importFile" accept=".json" onchange="importData(event)" style="display: none;">
            <button onclick="document.getElementById('importFile').click()" class="btn btn-secondary">استيراد البيانات</button>
          </div>
        </div>

        <div class="settings-section">
          <h3>معلومات البرنامج</h3>
          <div class="info-grid">
            <div class="info-item">
              <label>الإصدار:</label>
              <span>1.0.0</span>
            </div>
            <div class="info-item">
              <label>آخر تحديث:</label>
              <span id="lastUpdate">-</span>
            </div>
            <div class="info-item">
              <label>حجم قاعدة البيانات:</label>
              <span id="dbSize">-</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- تحميل السكريبتات -->
  <script src="scripts/compatibility.js"></script>
  <script src="scripts/database.js"></script>
  <script src="scripts/ui.js"></script>
  <script src="scripts/app.js"></script>

  <!-- تسجيل Service Worker ودعم PWA -->
  <script>
    // تسجيل Service Worker
    if ('serviceWorker' in navigator) {
      window.addEventListener('load', function() {
        navigator.serviceWorker.register('./sw.js')
          .then(function(registration) {
            console.log('تم تسجيل Service Worker بنجاح:', registration.scope);

            // فحص التحديثات
            registration.addEventListener('updatefound', function() {
              var newWorker = registration.installing;
              newWorker.addEventListener('statechange', function() {
                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                  // إشعار المستخدم بوجود تحديث
                  if (confirm('يتوفر تحديث جديد للتطبيق. هل تريد إعادة التحميل؟')) {
                    window.location.reload();
                  }
                }
              });
            });
          })
          .catch(function(error) {
            console.log('فشل تسجيل Service Worker:', error);
          });
      });
    }

    // دعم تثبيت التطبيق (PWA)
    var deferredPrompt;
    window.addEventListener('beforeinstallprompt', function(e) {
      e.preventDefault();
      deferredPrompt = e;

      // إظهار زر التثبيت
      var installButton = document.createElement('button');
      installButton.textContent = '📱 تثبيت التطبيق';
      installButton.className = 'btn btn-primary install-btn';
      installButton.style.cssText = 'position: fixed; bottom: 20px; left: 20px; z-index: 1000; border-radius: 25px;';

      installButton.addEventListener('click', function() {
        deferredPrompt.prompt();
        deferredPrompt.userChoice.then(function(choiceResult) {
          if (choiceResult.outcome === 'accepted') {
            console.log('تم قبول تثبيت التطبيق');
          }
          deferredPrompt = null;
          installButton.remove();
        });
      });

      document.body.appendChild(installButton);

      // إخفاء الزر بعد 10 ثوان
      setTimeout(function() {
        if (installButton.parentNode) {
          installButton.remove();
        }
      }, 10000);
    });

    // فحص دعم المتصفح
    function checkBrowserSupport() {
      var features = {
        localStorage: typeof(Storage) !== "undefined",
        json: typeof JSON !== 'undefined',
        serviceWorker: 'serviceWorker' in navigator
      };

      var unsupported = [];
      for (var feature in features) {
        if (!features[feature]) {
          unsupported.push(feature);
        }
      }

      if (unsupported.length > 0) {
        console.warn('ميزات غير مدعومة:', unsupported);
        setTimeout(function() {
          alert('تحذير: بعض ميزات التطبيق قد لا تعمل بشكل صحيح في هذا المتصفح. يُنصح بتحديث المتصفح.');
        }, 2000);
      }
    }

    // تحديث الإحصائيات عند التحميل
    document.addEventListener('DOMContentLoaded', function() {
      // فحص دعم المتصفح
      checkBrowserSupport();

      // تحديث إحصائيات الصفحة الرئيسية
      try {
        var totalRecords = archiveData.length;
        var today = new Date();
        var todayRecords = archiveData.filter(function(record) {
          var recordDate = new Date(record.year, record.month - 1, record.day);
          return recordDate.toDateString() === today.toDateString();
        }).length;

        var departments = [];
        for (var i = 0; i < archiveData.length; i++) {
          if (archiveData[i].department && departments.indexOf(archiveData[i].department) === -1) {
            departments.push(archiveData[i].department);
          }
        }

        var totalElement = document.getElementById('totalRecordsCount');
        var todayElement = document.getElementById('todayRecordsCount');
        var deptElement = document.getElementById('departmentsCount');

        if (totalElement) totalElement.textContent = totalRecords;
        if (todayElement) todayElement.textContent = todayRecords;
        if (deptElement) deptElement.textContent = departments.length;

        // تحديث معلومات البرنامج
        var dataSize = JSON.stringify(archiveData).length;
        var dbSizeElement = document.getElementById('dbSize');
        var lastUpdateElement = document.getElementById('lastUpdate');

        if (dbSizeElement) {
          dbSizeElement.textContent = (dataSize / 1024).toFixed(2) + ' KB';
        }
        if (lastUpdateElement) {
          lastUpdateElement.textContent = new Date().toLocaleDateString('ar-SA');
        }
      } catch(e) {
        console.warn('خطأ في تحديث الإحصائيات:', e);
      }
    });
  </script>
</body>
</html>
