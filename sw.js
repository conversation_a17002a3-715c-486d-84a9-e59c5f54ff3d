// Service Worker للعمل بدون اتصال ودعم PWA
const CACHE_NAME = 'archive-app-v2.0.0';
const urlsToCache = [
  './',
  './index.html',
  './styles/main.css',
  './styles/themes.css',
  './styles/animations.css',
  './scripts/app.js',
  './manifest.json',
  './assets/favicon.ico'
];

// تثبيت Service Worker
self.addEventListener('install', function(event) {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(function(cache) {
        console.log('تم فتح الذاكرة المؤقتة');
        return cache.addAll(urlsToCache);
      })
      .catch(function(error) {
        console.error('خطأ في تثبيت Service Worker:', error);
      })
  );
});

// تفعيل Service Worker
self.addEventListener('activate', function(event) {
  event.waitUntil(
    caches.keys().then(function(cacheNames) {
      return Promise.all(
        cacheNames.map(function(cacheName) {
          if (cacheName !== CACHE_NAME) {
            console.log('حذف الذاكرة المؤقتة القديمة:', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
});

// اعتراض الطلبات
self.addEventListener('fetch', function(event) {
  event.respondWith(
    caches.match(event.request)
      .then(function(response) {
        // إرجاع الملف من الذاكرة المؤقتة إذا وُجد
        if (response) {
          return response;
        }

        // محاولة جلب الملف من الشبكة
        return fetch(event.request).then(function(response) {
          // فحص صحة الاستجابة
          if (!response || response.status !== 200 || response.type !== 'basic') {
            return response;
          }

          // نسخ الاستجابة
          var responseToCache = response.clone();

          caches.open(CACHE_NAME)
            .then(function(cache) {
              cache.put(event.request, responseToCache);
            });

          return response;
        }).catch(function() {
          // إرجاع صفحة بديلة في حالة عدم توفر الاتصال
          if (event.request.destination === 'document') {
            return caches.match('./index.html');
          }
        });
      })
  );
});

// معالجة رسائل من التطبيق الرئيسي
self.addEventListener('message', function(event) {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
});

// إشعار بالتحديثات
self.addEventListener('updatefound', function() {
  console.log('تم العثور على تحديث جديد');
});

// معالجة الأخطاء
self.addEventListener('error', function(event) {
  console.error('خطأ في Service Worker:', event.error);
});

// دعم المزامنة في الخلفية
self.addEventListener('sync', function(event) {
  if (event.tag === 'background-sync') {
    event.waitUntil(doBackgroundSync());
  }
});

function doBackgroundSync() {
  // يمكن إضافة منطق المزامنة هنا
  return Promise.resolve();
}

// دعم الإشعارات Push
self.addEventListener('push', function(event) {
  if (event.data) {
    const data = event.data.json();
    const options = {
      body: data.body,
      icon: './assets/icon-192.png',
      badge: './assets/icon-192.png',
      vibrate: [100, 50, 100],
      data: {
        dateOfArrival: Date.now(),
        primaryKey: 1
      },
      actions: [
        {
          action: 'explore',
          title: 'فتح التطبيق',
          icon: './assets/icon-192.png'
        },
        {
          action: 'close',
          title: 'إغلاق',
          icon: './assets/icon-192.png'
        }
      ]
    };

    event.waitUntil(
      self.registration.showNotification(data.title, options)
    );
  }
});

// معالجة النقر على الإشعارات
self.addEventListener('notificationclick', function(event) {
  event.notification.close();

  if (event.action === 'explore') {
    event.waitUntil(
      clients.openWindow('./')
    );
  }
});

// تنظيف الذاكرة المؤقتة
function cleanupCache() {
  return caches.keys().then(function(cacheNames) {
    return Promise.all(
      cacheNames.map(function(cacheName) {
        if (cacheName.startsWith('archive-app-') && cacheName !== CACHE_NAME) {
          return caches.delete(cacheName);
        }
      })
    );
  });
}

// تشغيل تنظيف الذاكرة المؤقتة كل 24 ساعة
setInterval(cleanupCache, 24 * 60 * 60 * 1000);
