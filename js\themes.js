// تعريف الثيمات المتاحة
const themes = {
  blue: {
    name: 'الأزرق الكلاسيكي',
    primaryColor: '#3498db',
    secondaryColor: '#2c3e50',
    accentColor: '#e74c3c',
    successColor: '#27ae60',
    warningColor: '#f39c12',
    backgroundColor: '#ecf0f1',
    textColor: '#2c3e50',
    cardColor: '#ffffff'
  },
  green: {
    name: 'الأخضر الطبيعي',
    primaryColor: '#27ae60',
    secondaryColor: '#16a085',
    accentColor: '#e67e22',
    successColor: '#2ecc71',
    warningColor: '#f1c40f',
    backgroundColor: '#e8f5e8',
    textColor: '#2c3e50',
    cardColor: '#ffffff'
  },
  purple: {
    name: 'البنفسجي العصري',
    primaryColor: '#9b59b6',
    secondaryColor: '#8e44ad',
    accentColor: '#e74c3c',
    successColor: '#27ae60',
    warningColor: '#f39c12',
    backgroundColor: '#f4f0f7',
    textColor: '#2c3e50',
    cardColor: '#ffffff'
  },
  orange: {
    name: 'البرتقالي النابض',
    primaryColor: '#f39c12',
    secondaryColor: '#e67e22',
    accentColor: '#e74c3c',
    successColor: '#27ae60',
    warningColor: '#f1c40f',
    backgroundColor: '#fdf6e3',
    textColor: '#2c3e50',
    cardColor: '#ffffff'
  }
};

// تطبيق الثيم
function changeTheme(themeName) {
  if (!themes[themeName]) {
    console.error('الثيم غير موجود:', themeName);
    return;
  }
  
  const theme = themes[themeName];
  const root = document.documentElement;
  
  // تطبيق متغيرات CSS
  Object.entries(theme).forEach(([key, value]) => {
    if (key !== 'name') {
      const cssVar = key.replace(/([A-Z])/g, '-$1').toLowerCase();
      root.style.setProperty(`--${cssVar}`, value);
    }
  });
  
  // حفظ الثيم المحدد
  saveThemeToStorage(themeName);
  
  // تحديث الواجهة
  updateThemeUI(themeName);
  
  // عرض رسالة التأكيد
  showMessage(`تم تطبيق ثيم ${theme.name}`, 'success');
}

// تحديث واجهة الثيم
function updateThemeUI(currentTheme) {
  // إزالة التحديد من جميع الخيارات
  document.querySelectorAll('.theme-option').forEach(option => {
    option.classList.remove('selected');
  });
  
  // تحديد الثيم الحالي
  const selectedOption = document.querySelector(`.theme-option[onclick="changeTheme('${currentTheme}')"]`);
  if (selectedOption) {
    selectedOption.classList.add('selected');
  }
}

// تطبيق الثيم المحفوظ عند تحميل الصفحة
function applyStoredTheme() {
  const storedTheme = loadThemeFromStorage();
  changeTheme(storedTheme);
}

// إنشاء ثيم مخصص
function createCustomTheme(customColors) {
  const customTheme = {
    name: 'ثيم مخصص',
    ...customColors
  };
  
  themes.custom = customTheme;
  changeTheme('custom');
}

// تصدير إعدادات الثيم
function exportThemeSettings() {
  const currentTheme = loadThemeFromStorage();
  const themeData = {
    selectedTheme: currentTheme,
    themeSettings: themes[currentTheme],
    timestamp: new Date().toISOString()
  };
  
  const dataStr = JSON.stringify(themeData, null, 2);
  const dataBlob = new Blob([dataStr], {type: 'application/json'});
  
  const link = document.createElement('a');
  link.href = URL.createObjectURL(dataBlob);
  link.download = `theme_settings_${new Date().toISOString().split('T')[0]}.json`;
  link.click();
  
  showMessage('تم تصدير إعدادات الثيم', 'success');
}

// استيراد إعدادات الثيم
function importThemeSettings() {
  const input = document.createElement('input');
  input.type = 'file';
  input.accept = '.json';
  
  input.onchange = function(e) {
    const file = e.target.files[0];
    if (!file) return;
    
    const reader = new FileReader();
    reader.onload = function(e) {
      try {
        const themeData = JSON.parse(e.target.result);
        
        if (themeData.selectedTheme && themeData.themeSettings) {
          themes[themeData.selectedTheme] = themeData.themeSettings;
          changeTheme(themeData.selectedTheme);
          showMessage('تم استيراد إعدادات الثيم بنجاح', 'success');
        } else {
          throw new Error('تنسيق ملف الثيم غير صحيح');
        }
      } catch (error) {
        showMessage('خطأ في قراءة ملف الثيم', 'error');
      }
    };
    
    reader.readAsText(file);
  };
  
  input.click();
}

// تبديل الثيم تلقائياً حسب الوقت
function autoThemeSwitch() {
  const hour = new Date().getHours();
  
  if (hour >= 6 && hour < 12) {
    // الصباح - ثيم فاتح
    changeTheme('blue');
  } else if (hour >= 12 && hour < 18) {
    // الظهر - ثيم عادي
    changeTheme('green');
  } else if (hour >= 18 && hour < 22) {
    // المساء - ثيم دافئ
    changeTheme('orange');
  } else {
    // الليل - ثيم داكن
    changeTheme('purple');
  }
}

// إضافة تأثيرات بصرية متقدمة
function addVisualEffects() {
  // تأثير الجسيمات في الخلفية
  const canvas = document.createElement('canvas');
  canvas.id = 'backgroundCanvas';
  canvas.style.position = 'fixed';
  canvas.style.top = '0';
  canvas.style.left = '0';
  canvas.style.width = '100%';
  canvas.style.height = '100%';
  canvas.style.zIndex = '-1';
  canvas.style.pointerEvents = 'none';
  
  document.body.appendChild(canvas);
  
  const ctx = canvas.getContext('2d');
  canvas.width = window.innerWidth;
  canvas.height = window.innerHeight;
  
  const particles = [];
  const particleCount = 50;
  
  // إنشاء الجسيمات
  for (let i = 0; i < particleCount; i++) {
    particles.push({
      x: Math.random() * canvas.width,
      y: Math.random() * canvas.height,
      radius: Math.random() * 3 + 1,
      speed: Math.random() * 0.5 + 0.1,
      opacity: Math.random() * 0.5 + 0.2
    });
  }
  
  // رسم الجسيمات
  function animateParticles() {
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    particles.forEach(particle => {
      ctx.beginPath();
      ctx.arc(particle.x, particle.y, particle.radius, 0, Math.PI * 2);
      ctx.fillStyle = `rgba(52, 152, 219, ${particle.opacity})`;
      ctx.fill();
      
      particle.y -= particle.speed;
      
      if (particle.y < 0) {
        particle.y = canvas.height;
        particle.x = Math.random() * canvas.width;
      }
    });
    
    requestAnimationFrame(animateParticles);
  }
  
  animateParticles();
  
  // تحديث حجم Canvas عند تغيير حجم النافذة
  window.addEventListener('resize', () => {
    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;
  });
}

// إضافة أنماط CSS للثيمات
const themeStyles = document.createElement('style');
themeStyles.textContent = `
  .theme-option.selected {
    border: 3px solid var(--primary-color);
    box-shadow: 0 0 15px rgba(52, 152, 219, 0.5);
    transform: scale(1.05);
  }
  
  .theme-option.selected .theme-preview {
    animation: pulse 1s infinite;
  }
  
  .dark-mode {
    --background-color: #1a1a1a;
    --text-color: #ffffff;
    --card-color: #2d2d2d;
    --shadow-color: rgba(255, 255, 255, 0.1);
  }
  
  .high-contrast {
    --primary-color: #000000;
    --secondary-color: #ffffff;
    --text-color: #000000;
    --background-color: #ffffff;
    --card-color: #f0f0f0;
  }
  
  @media (prefers-color-scheme: dark) {
    body.auto-theme {
      --background-color: #1a1a1a;
      --text-color: #ffffff;
      --card-color: #2d2d2d;
    }
  }
`;
document.head.appendChild(themeStyles);

// تفعيل التأثيرات البصرية عند التحميل
document.addEventListener('DOMContentLoaded', () => {
  addVisualEffects();
});