# تعليمات تشغيل برنامج الأرشفة الإلكترونية

## المطور: المحاسب المبرمج علي عاجل خشان المحنة

---

## 🚀 طرق تشغيل البرنامج

### الطريقة الأولى: التشغيل المباشر من المتصفح (الأسهل)

1. **افتح ملف `index.html`**
   - اضغط مرتين على ملف `index.html`
   - أو اسحبه إلى أي متصفح
   - أو اضغط بالزر الأيمن واختر "فتح باستخدام المتصفح"

2. **البرنامج سيعمل فوراً!**
   - ستظهر الصفحة الرئيسية
   - جميع الميزات متاحة
   - البيانات تُحفظ في المتصفح تلقائياً

---

### الطريقة الثانية: تشغيل كتطبيق سطح المكتب (Electron)

#### المتطلبات:
- Node.js (تحميل من: https://nodejs.org)

#### خطوات التثبيت:

1. **افتح موجه الأوامر (Command Prompt)**
   - اضغط `Win + R`
   - اكتب `cmd` واضغط Enter

2. **انتقل لمجلد البرنامج**
   ```bash
   cd "مسار_مجلد_البرنامج"
   ```

3. **تثبيت المتطلبات**
   ```bash
   npm install
   ```

4. **تشغيل البرنامج**
   ```bash
   npm start
   ```

#### مميزات إصدار سطح المكتب:
- ✅ حفظ تلقائي في القرص D
- ✅ نسخ احتياطية تلقائية
- ✅ استيراد/تصدير ملفات
- ✅ عمل بدون إنترنت
- ✅ أداء أفضل

---

### الطريقة الثالثة: تشغيل على خادم محلي

#### باستخدام Python:
```bash
# Python 3
python -m http.server 8000

# Python 2
python -m SimpleHTTPServer 8000
```

#### باستخدام Node.js:
```bash
# تثبيت http-server
npm install -g http-server

# تشغيل الخادم
http-server -p 8000
```

#### باستخدام PHP:
```bash
php -S localhost:8000
```

**ثم افتح المتصفح واذهب إلى:** `http://localhost:8000`

---

## 💾 مواقع حفظ البيانات

### في المتصفح:
- **localStorage**: البيانات محفوظة في المتصفح
- **مسار Windows**: `%APPDATA%\Local\[Browser]\User Data\Default\Local Storage`
- **مسار Mac**: `~/Library/Application Support/[Browser]/Default/Local Storage`

### في إصدار سطح المكتب:
- **القرص D**: `D:\ArchiveData\`
- **ملف البيانات**: `D:\ArchiveData\archive_data.json`
- **النسخ الاحتياطية**: `D:\ArchiveData\backups\`

---

## 🔧 حل المشاكل الشائعة

### المشكلة: البرنامج لا يفتح
**الحلول:**
1. تأكد من وجود جميع الملفات
2. جرب متصفح آخر
3. تأكد من تفعيل JavaScript
4. امسح ذاكرة التخزين المؤقت

### المشكلة: لا يتم حفظ البيانات
**الحلول:**
1. تأكد من إعدادات الخصوصية في المتصفح
2. اسمح بـ localStorage
3. جرب التشغيل من خادم محلي
4. استخدم إصدار سطح المكتب

### المشكلة: التصميم يظهر خطأ
**الحلول:**
1. تأكد من تحميل ملفات CSS
2. امسح ذاكرة المتصفح
3. تأكد من دعم المتصفح لـ CSS3
4. جرب متصفح حديث

### المشكلة: البرنامج بطيء
**الحلول:**
1. سيتم تفعيل وضع الأداء المنخفض تلقائياً
2. أغلق التطبيقات الأخرى
3. استخدم متصفح أحدث
4. استخدم إصدار سطح المكتب

---

## 📱 التثبيت على الأجهزة المحمولة

### Android:
1. افتح Chrome أو Firefox
2. اذهب إلى البرنامج
3. اضغط على "إضافة إلى الشاشة الرئيسية"
4. سيعمل كتطبيق مستقل

### iOS:
1. افتح Safari
2. اذهب إلى البرنامج
3. اضغط على زر المشاركة
4. اختر "إضافة إلى الشاشة الرئيسية"

---

## 🌐 المتصفحات المدعومة

### مدعوم بالكامل:
- ✅ Chrome 30+
- ✅ Firefox 25+
- ✅ Safari 7+
- ✅ Edge (جميع الإصدارات)
- ✅ Opera 17+

### مدعوم مع قيود:
- ⚠️ Internet Explorer 9+

---

## 📞 الدعم الفني

### في حالة وجود مشاكل:
1. تأكد من تحديث المتصفح
2. امسح ذاكرة التخزين المؤقت
3. جرب متصفح آخر
4. تأكد من تفعيل JavaScript

### معلومات النظام:
- اضغط F12 لفتح أدوات المطور
- راجع وحدة التحكم (Console) للأخطاء

---

## ✅ التحقق من التثبيت

بعد فتح البرنامج، تأكد من:
- ✅ ظهور الصفحة الرئيسية بشكل صحيح
- ✅ عمل جميع الأزرار والقوائم
- ✅ إمكانية إضافة سجل تجريبي
- ✅ حفظ البيانات بعد إعادة تحميل الصفحة

**إذا كان كل شيء يعمل بشكل صحيح، فقد تم التثبيت بنجاح! 🎉**

---

## 📝 ملاحظات مهمة

1. **النسخ الاحتياطية**: يُنصح بعمل نسخة احتياطية دورية
2. **التحديثات**: البرنامج يفحص التحديثات تلقائياً
3. **الأمان**: البيانات محفوظة محلياً فقط
4. **الخصوصية**: لا يتم إرسال أي بيانات للخارج

---

**المطور: المحاسب المبرمج علي عاجل خشان المحنة**  
**الإصدار: 2.0.0**  
**تاريخ الإصدار: 2024**
